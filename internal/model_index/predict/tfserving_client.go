package predict

import (
	"context"
	"fmt"
	"rtb_model_server/internal/zaplog"
	"time"

	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/connectivity"
	"google.golang.org/grpc/credentials/insecure"
)

// BatchPredictData 批量预测数据结构
type BatchPredictData struct {
	BatchSize      int                `json:"batch_size"`
	CreativeIDs    []int32            `json:"creative_ids"`    // 创意ID列表，保持顺序对齐
	RawFeatures    map[string][]int64 `json:"raw_features"`    // 每个特征是数组，支持批量
	HashedFeatures [][]uint64         `json:"hashed_features"` // 每个样本的hash特征列表（如果需要）
}

var ModelFeatureNames = []string{
	"absolute_pos", "ad_source", "adp_dim", "adp_id", "api_version",
	"app_bundle", "app_name", "app_package_name", "bid_id", "bidfloor",
	"budget_type_v1", "campaign_id", "cat_id", "city", "client_ip",
	"client_ipv6", "country", "creative_id", "creative_type", "dev_make",
	"dev_model", "dm_media_id", "dm_platform", "domob_bidfloor", "dsp_ad_slot",
	"dsp_advertiser_id", "dsp_bid", "dsp_cost_mod", "dsp_creative_id", "dsp_id",
	"exchange_id", "exp_id", "hour", "inventory_type", "media_bid_type",
	"province", "schain", "sponsor_id", "standard_make", "strategy_id",
	"surge_score", "tanx_ad_id", "tanx_group_id", "tanx_task_id", "template_id", "week_day",
}

// TFServingClient TensorFlow Serving gRPC 客户端
type TFServingClient struct {
	conn       *grpc.ClientConn
	client     PredictionServiceClient
	serverAddr string
	modelName  string
}

// NewTFServingClient 创建 TensorFlow Serving gRPC 客户端
func NewTFServingClient(serverAddr, modelName string) (*TFServingClient, error) {
	zaplog.Logger.Info("Creating TensorFlow Serving gRPC client",
		zap.String("server", serverAddr),
		zap.String("model", modelName))

	// 创建 gRPC 连接
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	conn, err := grpc.DialContext(ctx, serverAddr,
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithBlock(),
	)
	if err != nil {
		zaplog.Logger.Error("Failed to connect to TensorFlow Serving",
			zap.String("server", serverAddr),
			zap.Error(err))
		return nil, fmt.Errorf("grpc connection failed: %w", err)
	}

	// 创建 gRPC 客户端
	client := NewPredictionServiceClient(conn)

	tfClient := &TFServingClient{
		conn:       conn,
		client:     client,
		serverAddr: serverAddr,
		modelName:  modelName,
	}

	// 测试连接
	if err := tfClient.TestConnection(); err != nil {
		conn.Close()
		zaplog.Logger.Error("Failed to test TensorFlow Serving connection",
			zap.String("server", serverAddr),
			zap.Error(err))
		return nil, fmt.Errorf("connection test failed: %w", err)
	}

	zaplog.Logger.Info("TensorFlow Serving gRPC client created successfully",
		zap.String("server", serverAddr))

	return tfClient, nil
}

// Close 关闭连接
func (c *TFServingClient) Close() {
	if c.conn != nil {
		c.conn.Close()
		zaplog.Logger.Info("TensorFlow Serving gRPC connection closed")
	}
}

// TestConnection 测试连接状态
func (c *TFServingClient) TestConnection() error {
	if c.conn == nil {
		return fmt.Errorf("connection is nil")
	}

	state := c.conn.GetState()
	if state != connectivity.Ready && state != connectivity.Idle {
		return fmt.Errorf("connection not ready, state: %v", state)
	}

	return nil
}

// Predict 执行批量预测 - 严格按照 TensorFlow Serving 官网 gRPC 请求结构
func (c *TFServingClient) Predict(ctx context.Context, batchFeatures []map[string]interface{}) (map[string]interface{}, error) {
	// 检查连接状态
	if err := c.TestConnection(); err != nil {
		return nil, fmt.Errorf("connection check failed: %w", err)
	}

	if len(batchFeatures) == 0 {
		return nil, fmt.Errorf("empty batch features")
	}

	batchSize := len(batchFeatures)
	zaplog.Logger.Debug("Preparing batch prediction",
		zap.String("model", c.modelName),
		zap.Int("batch_size", batchSize))

	// 构建严格符合 TensorFlow Serving 官网标准的 gRPC 请求
	request := &PredictRequest{
		ModelSpec: &ModelSpec{
			Name:          c.modelName,
			SignatureName: "serving_default",
		},
		Inputs: make(map[string]*TensorProto),
	}

	// 固定的46个特征：45个预定义特征 + dsp_bid
	allFeatureNames := append(ModelFeatureNames, "dsp_bid")

	zaplog.Logger.Debug("Creating tensors for fixed 46 features",
		zap.Int("total_features", len(allFeatureNames)))

	// 为每个固定特征创建批量张量
	for _, featureName := range allFeatureNames {
		batchValues := make([]int64, batchSize)

		// 收集该特征在所有样本中的值，保持顺序对齐
		for i, features := range batchFeatures {
			if value, exists := features[featureName]; exists {
				// 转换为 int64
				if intVal, ok := c.convertToInt64(value); ok {
					batchValues[i] = intVal
				} else {
					batchValues[i] = c.getDefaultInt64Value(featureName)
				}
			} else {
				// 如果某个样本缺少该特征，使用默认值
				batchValues[i] = c.getDefaultInt64Value(featureName)
			}
		}

		// 创建符合 TensorFlow Serving 标准的张量
		// 参考官网：https://www.tensorflow.org/tfx/serving/api_rest
		tensor := &TensorProto{
			Dtype: DataType_DT_INT64,
			TensorShape: &TensorShapeProto{
				Dim: []*TensorShapeProto_Dim{
					{Size: int64(batchSize)}, // shape=(batch_size,)
				},
			},
			Int64Val: batchValues,
		}

		request.Inputs[featureName] = tensor
	}

	// 验证特征数量
	if len(request.Inputs) != 46 {
		zaplog.Logger.Warn("Feature count mismatch",
			zap.Int("expected", 46),
			zap.Int("actual", len(request.Inputs)))
	}

	zaplog.Logger.Debug("Sending gRPC batch prediction request",
		zap.String("model", c.modelName),
		zap.Int("batch_size", batchSize),
		zap.Int("feature_count", len(request.Inputs)),
		zap.Bool("strict_tfserving_format", true))

	// 发送 gRPC 请求
	response, err := c.client.Predict(ctx, request)
	if err != nil {
		zaplog.Logger.Error("gRPC batch prediction request failed",
			zap.String("model", c.modelName),
			zap.Error(err))
		return nil, fmt.Errorf("prediction failed: %w", err)
	}

	zaplog.Logger.Debug("gRPC batch prediction request completed",
		zap.String("model", c.modelName),
		zap.Int("output_count", len(response.Outputs)))

	// 转换响应
	result := make(map[string]interface{})
	for name, tensor := range response.Outputs {
		value, err := c.convertFromTensor(tensor)
		if err != nil {
			zaplog.Logger.Error("Failed to convert tensor to value",
				zap.String("output", name),
				zap.Error(err))
			continue
		}
		result[name] = value
	}

	return result, nil
}

// convertToTensor 将值转换为张量
func (c *TFServingClient) convertToTensor(value interface{}) (*TensorProto, error) {
	tensor := &TensorProto{}

	switch v := value.(type) {
	case []int64:
		tensor.Dtype = DataType_DT_INT64
		tensor.TensorShape = &TensorShapeProto{
			Dim: []*TensorShapeProto_Dim{{Size: int64(len(v))}},
		}
		tensor.Int64Val = v
	case []float32:
		tensor.Dtype = DataType_DT_FLOAT
		tensor.TensorShape = &TensorShapeProto{
			Dim: []*TensorShapeProto_Dim{{Size: int64(len(v))}},
		}
		tensor.FloatVal = v
	case []float64:
		tensor.Dtype = DataType_DT_DOUBLE
		tensor.TensorShape = &TensorShapeProto{
			Dim: []*TensorShapeProto_Dim{{Size: int64(len(v))}},
		}
		tensor.DoubleVal = v
	case int64:
		tensor.Dtype = DataType_DT_INT64
		tensor.TensorShape = &TensorShapeProto{
			Dim: []*TensorShapeProto_Dim{{Size: 1}},
		}
		tensor.Int64Val = []int64{v}
	case int32:
		tensor.Dtype = DataType_DT_INT64
		tensor.TensorShape = &TensorShapeProto{
			Dim: []*TensorShapeProto_Dim{{Size: 1}},
		}
		tensor.Int64Val = []int64{int64(v)}
	case int:
		tensor.Dtype = DataType_DT_INT64
		tensor.TensorShape = &TensorShapeProto{
			Dim: []*TensorShapeProto_Dim{{Size: 1}},
		}
		tensor.Int64Val = []int64{int64(v)}
	case float32:
		tensor.Dtype = DataType_DT_FLOAT
		tensor.TensorShape = &TensorShapeProto{
			Dim: []*TensorShapeProto_Dim{{Size: 1}},
		}
		tensor.FloatVal = []float32{v}
	case float64:
		tensor.Dtype = DataType_DT_DOUBLE
		tensor.TensorShape = &TensorShapeProto{
			Dim: []*TensorShapeProto_Dim{{Size: 1}},
		}
		tensor.DoubleVal = []float64{v}
	default:
		return nil, fmt.Errorf("unsupported type: %T", value)
	}

	return tensor, nil
}

// convertBatchToTensor 将批量值转换为张量
func (c *TFServingClient) convertBatchToTensor(featureName string, batchValues []interface{}) (*TensorProto, error) {
	if len(batchValues) == 0 {
		return nil, fmt.Errorf("empty batch values for feature %s", featureName)
	}

	batchSize := int64(len(batchValues))
	tensor := &TensorProto{}

	// 检查第一个非空值来确定数据类型
	var sampleValue interface{}
	for _, value := range batchValues {
		if value != nil {
			sampleValue = value
			break
		}
	}

	if sampleValue == nil {
		return nil, fmt.Errorf("all values are nil for feature %s", featureName)
	}

	switch sampleValue.(type) {
	case []int64:
		// 序列特征：shape=(batch_size, 序列长度)
		tensor.Dtype = DataType_DT_INT64
		var allValues []int64
		maxSeqLen := int64(0)

		// 找到最大序列长度
		for _, value := range batchValues {
			if seq, ok := value.([]int64); ok && len(seq) > int(maxSeqLen) {
				maxSeqLen = int64(len(seq))
			}
		}

		// 填充所有序列到相同长度
		for _, value := range batchValues {
			if seq, ok := value.([]int64); ok {
				// 添加原始序列
				allValues = append(allValues, seq...)
				// 填充到最大长度
				for i := len(seq); i < int(maxSeqLen); i++ {
					allValues = append(allValues, 0) // 用0填充
				}
			} else {
				// 如果不是序列，填充默认值
				for i := int64(0); i < maxSeqLen; i++ {
					allValues = append(allValues, 0)
				}
			}
		}

		tensor.TensorShape = &TensorShapeProto{
			Dim: []*TensorShapeProto_Dim{
				{Size: batchSize},
				{Size: maxSeqLen},
			},
		}
		tensor.Int64Val = allValues

	case int64, int32, int:
		// 单特征：shape=(batch_size)
		tensor.Dtype = DataType_DT_INT64
		tensor.TensorShape = &TensorShapeProto{
			Dim: []*TensorShapeProto_Dim{{Size: batchSize}},
		}

		var values []int64
		for _, value := range batchValues {
			switch v := value.(type) {
			case int64:
				values = append(values, v)
			case int32:
				values = append(values, int64(v))
			case int:
				values = append(values, int64(v))
			default:
				values = append(values, 0) // 默认值
			}
		}
		tensor.Int64Val = values

	case []float32:
		// 序列特征：shape=(batch_size, 序列长度)
		tensor.Dtype = DataType_DT_FLOAT
		var allValues []float32
		maxSeqLen := int64(0)

		// 找到最大序列长度
		for _, value := range batchValues {
			if seq, ok := value.([]float32); ok && len(seq) > int(maxSeqLen) {
				maxSeqLen = int64(len(seq))
			}
		}

		// 填充所有序列到相同长度
		for _, value := range batchValues {
			if seq, ok := value.([]float32); ok {
				allValues = append(allValues, seq...)
				for i := len(seq); i < int(maxSeqLen); i++ {
					allValues = append(allValues, 0.0)
				}
			} else {
				for i := int64(0); i < maxSeqLen; i++ {
					allValues = append(allValues, 0.0)
				}
			}
		}

		tensor.TensorShape = &TensorShapeProto{
			Dim: []*TensorShapeProto_Dim{
				{Size: batchSize},
				{Size: maxSeqLen},
			},
		}
		tensor.FloatVal = allValues

	case float32, float64:
		// 单特征：shape=(batch_size)
		tensor.Dtype = DataType_DT_FLOAT
		tensor.TensorShape = &TensorShapeProto{
			Dim: []*TensorShapeProto_Dim{{Size: batchSize}},
		}

		var values []float32
		for _, value := range batchValues {
			switch v := value.(type) {
			case float32:
				values = append(values, v)
			case float64:
				values = append(values, float32(v))
			default:
				values = append(values, 0.0) // 默认值
			}
		}
		tensor.FloatVal = values

	default:
		return nil, fmt.Errorf("unsupported batch type for feature %s: %T", featureName, sampleValue)
	}

	zaplog.Logger.Debug("Converted batch feature to tensor",
		zap.String("feature", featureName),
		zap.Int64("batch_size", batchSize),
		zap.String("dtype", tensor.Dtype.String()))

	return tensor, nil
}

// convertToInt64 将任意类型转换为 int64
func (c *TFServingClient) convertToInt64(value interface{}) (int64, bool) {
	switch v := value.(type) {
	case int64:
		return v, true
	case int32:
		return int64(v), true
	case int:
		return int64(v), true
	case float64:
		return int64(v), true
	case float32:
		return int64(v), true
	default:
		return 0, false
	}
}

// getDefaultInt64Value 获取特征的默认 int64 值
func (c *TFServingClient) getDefaultInt64Value(featureName string) int64 {
	// 基于模型期望的46个特征的默认值
	predefinedFeatures := map[string]int64{
		// 45个预定义特征（按字母顺序，与模型完全匹配）
		"absolute_pos": 0, "ad_source": 0, "adp_dim": 0, "adp_id": 0, "api_version": 0,
		"app_bundle": 0, "app_name": 0, "app_package_name": 0, "bid_id": 0, "bidfloor": 0,
		"budget_type_v1": 0, "campaign_id": 0, "cat_id": 0, "city": 0, "client_ip": 0,
		"client_ipv6": 0, "country": 0, "creative_id": 0, "creative_type": 0, "dev_make": 0,
		"dev_model": 0, "dm_media_id": 0, "dm_platform": 0, "domob_bidfloor": 0, "dsp_ad_slot": 0,
		"dsp_advertiser_id": 0, "dsp_cost_mod": 0, "dsp_creative_id": 0, "dsp_id": 0,
		"exchange_id": 0, "exp_id": 0, "hour": 0, "inventory_type": 0, "media_bid_type": 0,
		"province": 0, "schain": 0, "sponsor_id": 0, "standard_make": 0, "strategy_id": 0,
		"surge_score": 0, "tanx_ad_id": 0, "tanx_group_id": 0, "tanx_task_id": 0, "template_id": 0, "week_day": 0,
		// dsp_bid 特征
		"dsp_bid": 0,
	}

	if defaultVal, exists := predefinedFeatures[featureName]; exists {
		return defaultVal
	}

	return int64(0) // 默认返回 0
}

// convertFromTensor 将张量转换为值
func (c *TFServingClient) convertFromTensor(tensor *TensorProto) (interface{}, error) {
	switch tensor.Dtype {
	case DataType_DT_INT64:
		return tensor.Int64Val, nil
	case DataType_DT_FLOAT:
		return tensor.FloatVal, nil
	case DataType_DT_DOUBLE:
		return tensor.DoubleVal, nil
	default:
		return nil, fmt.Errorf("unsupported tensor type: %v", tensor.Dtype)
	}
}
