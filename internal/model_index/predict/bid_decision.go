package predict

import (
	"rtb_model_server/common/domob_thrift/enums"
	"rtb_model_server/common/domob_thrift/rtb_adinfo_types"
	"rtb_model_server/common/domob_thrift/rtb_types"
	ctx "rtb_model_server/internal/context"
	"rtb_model_server/internal/lib"
	"rtb_model_server/internal/model_index/filters"

	"github.com/pkg/errors"
)

type BidDecision struct {
	// 出价决策器
}

type BidResult struct {
	SearchImpId        int64              //曝光ID
	CreativeId         int32              //创意ID
	FilterType         filters.FilterType //过滤类型，在竞价过滤，则该字段不为0
	MatchedAdMatchType int64              //匹配上的广告位ID
	MatchedAdPosId     int64              //匹配上的广告位ID
	PreCtr             int64              //预估点击率
	PreAtr             int64              //预估广告点击转化率
	WinRate            float64            //预估的竞胜率
	CtrSource          int32              //预估点击率来源
	AtrSource          int32              //
	WinRateSource      int32              //

	SelectProb int64

	BidPrice      int64                    //盟元(百万分之一元)
	CpaBid        int64                    //盟元(百万分之一元)
	BidType       rtb_adinfo_types.BidType // 1系统优化出价,2人工出价
	CostType      enums.CostType
	MediaBidType  int //1表示cpm，2表示cpc，4表示cpa, 默认1，0情况同1为cpm
	MediaCostType int //由UI根据RTBStrategy.media_cost_type填充,默认值0表示by cpm，1表示by cpc
}

func (b *BidResult) SetFilterType(filterType filters.FilterType) {
	b.FilterType = filterType
}

func NewBidDecision() *BidDecision {
	return &BidDecision{}
}
func (b *BidDecision) Decision(requestCtx *ctx.RequestContext, allBidResults []*BidResult) (*BidResult, error) {

	// 如果没有任何有效出价，返回错误
	if len(allBidResults) == 0 {
		return nil, errors.New("no valid bid results")
	}

	// 找到出价最高的结果
	highestBid := allBidResults[0]
	for _, bidResult := range allBidResults[1:] {
		if bidResult.BidPrice > highestBid.BidPrice {
			highestBid = bidResult
		}
	}

	return highestBid, nil
}

// 这里会将预估的结果PredictResult封装成BidResult，随后会对BidResult进行排序
func (b *BidDecision) Sort(requestCtx *ctx.RequestContext, predictResults map[int32]PredictResult) ([]*BidResult, error) {
	var allBidResults map[int32][]*BidResult = make(map[int32][]*BidResult)

	// 遍历所有预测结果，获取每个创意的出价
	for cid, predictResult := range predictResults {
		bidResults, err := b.fillingBidResult(requestCtx, &predictResult)
		if err != nil {
			// 如果某个创意出价失败，继续处理其他创意
			continue
		}

		allBidResults[cid] = bidResults
	}

	// 如果没有任何有效出价，返回错误
	if len(allBidResults) == 0 {
		return nil, errors.New("no valid bid results")
	}

	// FIXME，这里需要对出价进行排序
	ret := make([]*BidResult, 0)
	for _, bidResult := range allBidResults {
		// 对于一次请求一般一个BidList的AdMatchType不一样，因此这里注意下，只返回了第一个
		// 如果需要多广告位返回，这里需要改造，将排序后的结果按照分广告位进行装填
		ret = append(ret, bidResult[0])
	}

	return ret, nil
}

func (b *BidDecision) fillingBidResult(requestCtx *ctx.RequestContext, predictResult *PredictResult) ([]*BidResult, error) {
	req := requestCtx.BidRequest
	creative, err := requestCtx.AdIndex.GetCreative(predictResult.CreativeId)
	if err != nil {
		return nil, errors.Wrap(err, "filling bid")
	}

	strategy, err := requestCtx.AdIndex.GetStrategy(creative.StrategyId)
	if err != nil {
		return nil, errors.Wrap(err, "filling bid")
	}

	campaign, err := requestCtx.AdIndex.GetCampaign(creative.CampaignId)
	if err != nil {
		return nil, errors.Wrap(err, "filling bid")
	}

	var results []*BidResult

	// 循环每个请求，查看是否能匹配上广告位
	for _, bid := range req.BidList {
		for _, container := range creative.Containers {
			if lib.InSlice[int64](bid.Request.AdMatchTypes, container.AdMatchType) {
				r := &BidResult{
					SearchImpId:        bid.Request.SearchImpId,
					CreativeId:         predictResult.CreativeId,
					MatchedAdMatchType: creative.AdMatchType,
					MatchedAdPosId:     container.AdMatchType,
					CostType:           strategy.CostType,
					BidType:            rtb_adinfo_types.BidType_BIT_SYS_AUTO,
					PreCtr:             predictResult.PreCtr,
					PreAtr:             predictResult.PreCvr,
				}

				// 计算出价
				bidPrice, cpaBid := b.getBidPrice(campaign, strategy, bid.Request, predictResult)
				if bidPrice <= 0 && cpaBid <= 0 {
					continue // 跳过无效出价
				}
				r.BidPrice = bidPrice
				r.CpaBid = cpaBid

				// 设置媒体出价类型和成本类型
				r.MediaBidType = int(bid.Request.SupportMediaBidType)
				if r.MediaBidType == 0 {
					r.MediaBidType = 1 // 默认CPM
				}
				r.MediaCostType = int(strategy.MediaCostType)
				results = append(results, r)
				break
			}
		}
	}

	if len(results) == 0 {
		return nil, errors.New("no matched ad match type or valid bid price")
	}

	return results, nil
}

// getCpmBidPrice 获取CPM出价

func (b *BidDecision) getBidPrice(campaign *rtb_adinfo_types.RTBCampaign, strategy *rtb_adinfo_types.RTBStrategy, req *rtb_types.RTBRequestInfo, predictResult *PredictResult) (int64, int64) {
	// 检查策略和媒体出价类型的兼容性
	mediaBidType := req.SupportMediaBidType
	if mediaBidType == 0 {
		mediaBidType = 1 // 默认CPM
	}
	// 检查策略是否支持媒体要求的出价类型
	if !b.isMediaBidTypeSupported(strategy.MediaBidType, mediaBidType) {
		return 0, 0 // 不支持的出价类型组合
	}
	basePrice := strategy.Price
	if basePrice <= 0 && strategy.InternalAcostLimit <= 0 {
		return 0, 0
	}
	// 根据媒体出价类型和策略成本类型计算最终出价
	//fixme 未考虑单位问题
	switch campaign.CostType {
	case enums.CostType_CT_CPM:
		return b.calculateCpmBid(strategy, predictResult, basePrice)
	case enums.CostType_CT_CPC:
		return b.calculateCpcBid(strategy, predictResult, basePrice)
	case enums.CostType_CT_OCPM: //CPA
		return b.calculateOcpmBid(strategy, predictResult, basePrice)
	case enums.CostType_CT_OCPC:
		return b.calculateOcpcBid(strategy, predictResult, basePrice)
	default:
		return basePrice, 0
	}
}

// isMediaBidTypeSupported 检查策略是否支持媒体要求的出价类型
func (b *BidDecision) isMediaBidTypeSupported(strategyMediaBidType, requestMediaBidType int32) bool {
	// 策略的media_bid_type是位运算方式：1支持CPM，2支持CPC，4支持CPA
	// 3(1+2)支持CPM、CPC，5(1+4)支持CPM、CPA，6(2+4)支持CPC、CPA，7(1+2+4)支持全部
	return (strategyMediaBidType & requestMediaBidType) != 0
}

// calculateCpmBid 计算CPM出价,CPM策略直接返回基础价格
func (b *BidDecision) calculateCpmBid(strategy *rtb_adinfo_types.RTBStrategy, predictResult *PredictResult, basePrice int64) (int64, int64) {
	return basePrice, 0
}

// calculateCpcBid 计算CPC出价，CPC分为媒体CPC以及DM CPC，通过media_bid_type区分
func (b *BidDecision) calculateCpcBid(strategy *rtb_adinfo_types.RTBStrategy, predictResult *PredictResult, basePrice int64) (int64, int64) {
	switch strategy.MediaBidType {
	case 1: //CPM，需要根据媒体CPC策略进行转换
		// CPM策略转CPC：基础价格 * 预估CTR
		if predictResult.PreCtr > 0 {
			return int64(float64(basePrice) * float64(predictResult.PreCtr)), 0
		}
		return 0, 0
	case 2: //媒体CPC直接返回价格
		return basePrice, 0
	default:
		return basePrice, 0
	}
}

// calculateOcpmBid 计算CPA出价，实际是CPA
func (b *BidDecision) calculateOcpmBid(strategy *rtb_adinfo_types.RTBStrategy, predictResult *PredictResult, basePrice int64) (int64, int64) {
	return basePrice, strategy.InternalAcostLimit
}

// calculateCpcBid 计算CPA出价，返回A价格同时增加CPC/CPM价格返回
func (b *BidDecision) calculateOcpcBid(strategy *rtb_adinfo_types.RTBStrategy, predictResult *PredictResult, basePrice int64) (int64, int64) {
	switch strategy.MediaBidType {
	case 1: //CPM，需要根据媒体CPC策略进行转换
		if predictResult.PreCtr > 0 {
			return int64(float64(basePrice) * float64(predictResult.PreCtr)), 0
		}
		return 0, strategy.InternalAcostLimit
	case 2: //媒体CPC直接返回价格
		return basePrice, strategy.InternalAcostLimit
	default:
		return basePrice, strategy.InternalAcostLimit
	}
}
