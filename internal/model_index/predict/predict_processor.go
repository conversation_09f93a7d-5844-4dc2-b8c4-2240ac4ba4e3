package predict

import (
	"context"
	"fmt"
	"rtb_model_server/conf"
	ctx "rtb_model_server/internal/context"
	"rtb_model_server/internal/zaplog"
	"sync"
	"time"

	"go.uber.org/zap"
)

type PredictProcessor struct {
	tfClient         *TFServingClient
	featureProcessor *FeatureProcessor
	monitorStop      chan struct{}
	monitorWg        sync.WaitGroup
}

func NewPredictProcessor() *PredictProcessor {
	config := conf.GlobalConfig.PredictServer

	// 创建 TensorFlow Serving 客户端
	tfClient, err := NewTFServingClient(config.TFServingAddr, config.ModelName)
	if err != nil {
		zaplog.Logger.Error("Failed to create TensorFlow Serving client",
			zap.String("addr", config.TFServingAddr),
			zap.String("model", config.ModelName),
			zap.Error(err))
		// 如果创建失败，返回 nil 或者 panic，根据项目需求决定
		panic(fmt.Sprintf("Failed to create TensorFlow Serving client: %v", err))
	}

	// 创建特征处理器
	featureProcessor, err := NewFeatureProcessor(config.FeatureConfigPath)
	if err != nil {
		zaplog.Logger.Error("Failed to create feature processor",
			zap.String("config_path", config.FeatureConfigPath),
			zap.Error(err))
		// 如果创建失败，返回 nil 或者 panic，根据项目需求决定
		panic(fmt.Sprintf("Failed to create feature processor: %v", err))
	}

	p := &PredictProcessor{
		tfClient:         tfClient,
		featureProcessor: featureProcessor,
		monitorStop:      make(chan struct{}),
	}

	// 启动监控协程
	p.startMonitor()

	zaplog.Logger.Info("PredictProcessor initialized successfully",
		zap.String("tf_serving_addr", config.TFServingAddr),
		zap.String("model_name", config.ModelName),
		zap.String("feature_config", config.FeatureConfigPath))

	return p
}

// Close 关闭处理器
func (p *PredictProcessor) Close() {
	// 停止监控协程
	close(p.monitorStop)
	p.monitorWg.Wait()

	// 关闭 TensorFlow Serving 客户端
	if p.tfClient != nil {
		p.tfClient.Close()
	}

	zaplog.Logger.Info("PredictProcessor closed")
}

// GetStats 获取处理器统计信息
func (p *PredictProcessor) GetStats() map[string]interface{} {
	stats := make(map[string]interface{})

	// 测试 TensorFlow Serving 连接状态
	if p.tfClient != nil {
		if err := p.tfClient.TestConnection(); err != nil {
			stats["tf_serving_status"] = "disconnected"
			stats["tf_serving_error"] = err.Error()
		} else {
			stats["tf_serving_status"] = "connected"
		}
	} else {
		stats["tf_serving_status"] = "not_initialized"
	}

	// 特征处理器统计
	if p.featureProcessor != nil {
		stats["feature_count"] = p.featureProcessor.GetFeatureCount()
		stats["hash_feature_count"] = p.featureProcessor.GetHashFeatureCount()
		stats["raw_feature_count"] = p.featureProcessor.GetRawFeatureCount()
	}

	return stats
}

// startMonitor 启动监控协程
func (p *PredictProcessor) startMonitor() {
	p.monitorWg.Add(1)
	go func() {
		defer p.monitorWg.Done()

		// 监控间隔，可以根据需要调整
		monitorInterval := 30 * time.Second
		ticker := time.NewTicker(monitorInterval)
		defer ticker.Stop()

		zaplog.Logger.Info("TensorFlow Serving monitor started")

		for {
			select {
			case <-p.monitorStop:
				zaplog.Logger.Info("TensorFlow Serving monitor stopped")
				return
			case <-ticker.C:
				// 获取处理器统计信息
				stats := p.GetStats()
				zaplog.Logger.Info("TensorFlow Serving Stats",
					zap.Any("stats", stats))

				// 检查连接状态
				if status, ok := stats["tf_serving_status"].(string); ok && status != "connected" {
					zaplog.Logger.Warn("WARNING: TensorFlow Serving connection issue",
						zap.String("status", status))
				}
			}
		}
	}()
}

type PredictResult struct {
	CreativeId int32
	PreCtr     int64
	PreCvr     int64
	PreDeepCvr int64
}

// 调用预估服务，返回预估结果
func (p *PredictProcessor) Predict(requestCtx *ctx.RequestContext, creativeIds []int32) (map[int32]PredictResult, error) {
	zaplog.Logger.Debug("Starting batch prediction",
		zap.String("req_id", requestCtx.BidRequest.ReqId),
		zap.Int("creative_count", len(creativeIds)))

	if len(creativeIds) == 0 {
		return make(map[int32]PredictResult), nil
	}

	// 批量提取特征
	batchFeatures := make([]map[string]interface{}, 0, len(creativeIds))
	validCreativeIds := make([]int32, 0, len(creativeIds))

	for _, creativeId := range creativeIds {
		// 提取特征
		features, err := p.extractFeatures(requestCtx, creativeId)
		if err != nil {
			zaplog.Logger.Error("Failed to extract features",
				zap.Int32("creative_id", creativeId),
				zap.Error(err))
			continue
		}

		batchFeatures = append(batchFeatures, features)
		validCreativeIds = append(validCreativeIds, creativeId)
	}

	if len(batchFeatures) == 0 {
		zaplog.Logger.Warn("No valid features extracted",
			zap.String("req_id", requestCtx.BidRequest.ReqId))
		return make(map[int32]PredictResult), nil
	}

	// 批量调用 TensorFlow Serving 进行预测
	ctx, cancel := context.WithTimeout(context.Background(),
		time.Duration(conf.GlobalConfig.PredictServer.ConnTimeout)*time.Millisecond)
	defer cancel()

	batchPrediction, err := p.tfClient.Predict(ctx, batchFeatures)
	if err != nil {
		zaplog.Logger.Error("TensorFlow Serving batch prediction failed",
			zap.String("req_id", requestCtx.BidRequest.ReqId),
			zap.Int("batch_size", len(batchFeatures)),
			zap.Error(err))
		return nil, fmt.Errorf("batch prediction failed: %w", err)
	}

	// 解析批量预测结果
	results := make(map[int32]PredictResult)
	for i, creativeId := range validCreativeIds {
		result, err := p.parseBatchPredictionResult(creativeId, batchPrediction, i)
		if err != nil {
			zaplog.Logger.Error("Failed to parse batch prediction result",
				zap.Int32("creative_id", creativeId),
				zap.Int("batch_index", i),
				zap.Error(err))
			continue
		}

		results[creativeId] = result
	}

	zaplog.Logger.Debug("Batch prediction completed",
		zap.String("req_id", requestCtx.BidRequest.ReqId),
		zap.Int("success_count", len(results)),
		zap.Int("total_count", len(creativeIds)),
		zap.Int("batch_size", len(batchFeatures)))

	return results, nil
}

// extractFeatures 从 RTB 请求中提取特征
func (p *PredictProcessor) extractFeatures(requestCtx *ctx.RequestContext, creativeId int32) (map[string]interface{}, error) {
	bidReq := requestCtx.BidRequest
	now := time.Now()

	// 获取创意信息
	creative, err := requestCtx.AdIndex.GetCreative(creativeId)
	if err != nil {
		return nil, fmt.Errorf("get creative failed: %w", err)
	}

	// 获取策略信息
	strategy, err := requestCtx.AdIndex.GetStrategy(creative.StrategyId)
	if err != nil {
		return nil, fmt.Errorf("get strategy failed: %w", err)
	}

	// 构建原始特征 - 固定46个特征（45个预定义 + dsp_bid）
	// 严格按照 TensorFlow Serving 官网标准和您 demo 中的特征顺序
	rawFeatures := map[string]interface{}{
		// 45个预定义特征（按字母顺序，与模型 signature 完全匹配）
		"absolute_pos":      int64(0),                                     // 绝对位置
		"ad_source":         int64(0),                                     // 广告来源
		"adp_dim":           int64(0),                                     // 广告位尺寸
		"adp_id":            int64(0),                                     // 广告位ID
		"api_version":       int64(0),                                     // API版本
		"app_bundle":        p.convertStringToInt64(bidReq.App.AppBundle), // 应用包名
		"app_name":          p.convertStringToInt64(bidReq.App.AppBundle), // 应用名称
		"app_package_name":  p.convertStringToInt64(bidReq.App.AppBundle), // 应用包名
		"bid_id":            int64(0),                                     // 出价ID
		"bidfloor":          int64(0),                                     // 底价
		"budget_type_v1":    int64(0),                                     // 预算类型
		"campaign_id":       int64(creative.CampaignId),                   // 活动ID
		"cat_id":            int64(0),                                     // 分类ID
		"city":              int64(bidReq.Device.GeoCity),                 // 城市
		"client_ip":         p.convertIPToInt64(bidReq.Device.Ip),         // 客户端IP
		"client_ipv6":       int64(0),                                     // 客户端IPv6
		"country":           int64(bidReq.Device.GeoCountry),              // 国家
		"creative_id":       int64(creativeId),                            // 创意ID
		"creative_type":     int64(creative.DisplayType),                  // 创意类型
		"dev_make":          int64(0),                                     // 设备制造商
		"dev_model":         int64(0),                                     // 设备型号
		"dm_media_id":       int64(bidReq.App.DmMediaId),                  // 媒体ID
		"dm_platform":       int64(bidReq.Device.DmPlatform),              // 平台
		"domob_bidfloor":    int64(0),                                     // Domob底价
		"dsp_ad_slot":       int64(0),                                     // DSP广告位
		"dsp_advertiser_id": int64(0),                                     // DSP广告主ID
		"dsp_cost_mod":      int64(0),                                     // DSP成本模式
		"dsp_creative_id":   int64(0),                                     // DSP创意ID
		"dsp_id":            int64(1),                                     // DSP ID（固定值）
		"exchange_id":       int64(bidReq.ExchangeId),                     // 交易所ID
		"exp_id":            int64(0),                                     // 实验ID
		"hour":              int64(now.Hour()),                            // 小时
		"inventory_type":    int64(0),                                     // 库存类型
		"media_bid_type":    int64(strategy.MediaBidType),                 // 媒体出价类型
		"province":          int64(bidReq.Device.GeoRegion),               // 省份
		"schain":            int64(0),                                     // 供应链
		"sponsor_id":        int64(creative.SponsorId),                    // 赞助商ID
		"standard_make":     int64(0),                                     // 标准制造商
		"strategy_id":       int64(creative.StrategyId),                   // 策略ID
		"surge_score":       int64(0),                                     // 激增分数
		"tanx_ad_id":        int64(0),                                     // Tanx广告ID
		"tanx_group_id":     int64(0),                                     // Tanx组ID
		"tanx_task_id":      int64(0),                                     // Tanx任务ID
		"template_id":       int64(creative.TemplateId),                   // 模板ID
		"week_day":          int64(now.Weekday()),                         // 星期几

		// 第46个特征：dsp_bid（单独配置）
		"dsp_bid": int64(strategy.Price), // DSP出价
	}

	// 使用特征处理器处理特征
	processedFeatures, err := p.featureProcessor.ProcessFeatures(rawFeatures)
	if err != nil {
		return nil, fmt.Errorf("process features failed: %w", err)
	}

	zaplog.Logger.Debug("Features extracted",
		zap.Int32("creative_id", creativeId),
		zap.Int("raw_feature_count", len(rawFeatures)),
		zap.Int("processed_feature_count", len(processedFeatures)))

	return processedFeatures, nil
}

// parsePredictionResult 解析 TensorFlow Serving 预测结果
func (p *PredictProcessor) parsePredictionResult(creativeId int32, prediction map[string]interface{}) (PredictResult, error) {
	result := PredictResult{
		CreativeId: creativeId,
		PreCtr:     0,
		PreCvr:     0,
		PreDeepCvr: 0,
	}

	// 解析预测结果
	// TensorFlow Serving 返回的结果格式通常是 {"outputs": {"output_name": [[value1, value2, ...]]}}
	if outputs, ok := prediction["outputs"].(map[string]interface{}); ok {
		// 假设模型输出包含 ctr 和 cvr 预测
		if ctrOutput, exists := outputs["ctr"]; exists {
			if ctrValues, ok := ctrOutput.([][]float32); ok && len(ctrValues) > 0 && len(ctrValues[0]) > 0 {
				// 将 float32 转换为百万分之一的 int64
				result.PreCtr = int64(ctrValues[0][0] * 1000000)
			}
		}

		if cvrOutput, exists := outputs["cvr"]; exists {
			if cvrValues, ok := cvrOutput.([][]float32); ok && len(cvrValues) > 0 && len(cvrValues[0]) > 0 {
				// 将 float32 转换为百万分之一的 int64
				result.PreCvr = int64(cvrValues[0][0] * 1000000)
			}
		}

		if deepCvrOutput, exists := outputs["deep_cvr"]; exists {
			if deepCvrValues, ok := deepCvrOutput.([][]float32); ok && len(deepCvrValues) > 0 && len(deepCvrValues[0]) > 0 {
				// 将 float32 转换为百万分之一的 int64
				result.PreDeepCvr = int64(deepCvrValues[0][0] * 1000000)
			}
		}
	}

	zaplog.Logger.Debug("Parsed prediction result",
		zap.Int32("creative_id", creativeId),
		zap.Int64("pre_ctr", result.PreCtr),
		zap.Int64("pre_cvr", result.PreCvr),
		zap.Int64("pre_deep_cvr", result.PreDeepCvr))

	return result, nil
}

// parseBatchPredictionResult 解析批量 TensorFlow Serving 预测结果中的单个样本
func (p *PredictProcessor) parseBatchPredictionResult(creativeId int32, batchPrediction map[string]interface{}, batchIndex int) (PredictResult, error) {
	result := PredictResult{
		CreativeId: creativeId,
		PreCtr:     0,
		PreCvr:     0,
		PreDeepCvr: 0,
	}

	// 解析批量预测结果
	// TensorFlow Serving 返回的批量结果格式通常是 {"output_name": [batch_values]}
	for outputName, outputValues := range batchPrediction {
		switch outputName {
		case "ctr", "pre_ctr":
			if values, ok := outputValues.([]float32); ok && batchIndex < len(values) {
				// 将 float32 转换为百万分之一的 int64
				result.PreCtr = int64(values[batchIndex] * 1000000)
			} else if values, ok := outputValues.([]int64); ok && batchIndex < len(values) {
				result.PreCtr = values[batchIndex]
			}

		case "cvr", "pre_cvr":
			if values, ok := outputValues.([]float32); ok && batchIndex < len(values) {
				// 将 float32 转换为百万分之一的 int64
				result.PreCvr = int64(values[batchIndex] * 1000000)
			} else if values, ok := outputValues.([]int64); ok && batchIndex < len(values) {
				result.PreCvr = values[batchIndex]
			}

		case "deep_cvr", "pre_deep_cvr":
			if values, ok := outputValues.([]float32); ok && batchIndex < len(values) {
				// 将 float32 转换为百万分之一的 int64
				result.PreDeepCvr = int64(values[batchIndex] * 1000000)
			} else if values, ok := outputValues.([]int64); ok && batchIndex < len(values) {
				result.PreDeepCvr = values[batchIndex]
			}
		}
	}

	zaplog.Logger.Debug("Parsed batch prediction result",
		zap.Int32("creative_id", creativeId),
		zap.Int("batch_index", batchIndex),
		zap.Int64("pre_ctr", result.PreCtr),
		zap.Int64("pre_cvr", result.PreCvr),
		zap.Int64("pre_deep_cvr", result.PreDeepCvr))

	return result, nil
}

// convertIPToInt64 将IP地址转换为int64
func (p *PredictProcessor) convertIPToInt64(ip string) int64 {
	// 简单的IP转换，实际可能需要更复杂的处理
	// 这里可以使用hash或者其他转换方式
	if ip == "" {
		return 0
	}
	// 使用简单的hash转换
	hash := int64(0)
	for _, b := range []byte(ip) {
		hash = hash*31 + int64(b)
	}
	return hash
}

// convertStringToInt64 将字符串转换为int64
func (p *PredictProcessor) convertStringToInt64(s string) int64 {
	if s == "" {
		return 0
	}
	// 使用简单的hash转换
	hash := int64(0)
	for _, b := range []byte(s) {
		hash = hash*31 + int64(b)
	}
	return hash
}
