diff --git a/conf/conf.go b/conf/conf.go
index 3d54c2e..7a77b3d 100644
--- a/conf/conf.go
+++ b/conf/conf.go
@@ -18,11 +18,17 @@ type (
 		Compress   bool
 	}
 	PredictServer struct {
-		Addr         string `required:"true"`
-		ModelName    string `required:"true"`
+		// TensorFlow Serving gRPC 配置
+		TFServingAddr     string `required:"true"`
+		ModelName         string `required:"true"`
+		ConnTimeout       int    `default:"5000"` // milliseconds
+		MaxRetries        int    `default:"3"`
+		FeatureConfigPath string `default:"conf/feature_config.yaml"`
+
+		// 保留原有 Thrift 配置（如果需要兼容）
+		Addr         string `default:"127.0.0.1:3399"`
 		PoolSize     int    `default:"10"`
 		MaxIdleConns int    `default:"5"`
-		ConnTimeout  int    `default:"5000"` // milliseconds
 	}
 	// Model Index Configuration
 	ModelIndexConfig struct {
diff --git a/conf/rtb_model_server.yaml b/conf/rtb_model_server.yaml
index bd316ca..1fb31d4 100644
--- a/conf/rtb_model_server.yaml
+++ b/conf/rtb_model_server.yaml
@@ -3,11 +3,18 @@ Server:
   Port: 3398
 
 PredictServer:
+  # TensorFlow Serving gRPC 配置
+  TFServingAddr: "127.0.0.1:8500"
+  ModelName: "dnn_winr_v1"
+  ConnTimeout: 5000
+  MaxRetries: 3
+  # 特征配置文件路径
+  FeatureConfigPath: "conf/feature_config.yaml"
+
+  # 保留原有 Thrift 配置（如果需要兼容）
   Addr: "127.0.0.1:3399"
-  ModelName: "default_model"
   PoolSize: 10
   MaxIdleConns: 5
-  ConnTimeout: 1000
 
 LogConfig:
   LogPath: log/rtb_model_sever.log
diff --git a/go.mod b/go.mod
index ebe2fec..b406118 100644
--- a/go.mod
+++ b/go.mod
@@ -2,10 +2,9 @@ module rtb_model_server
 
 go 1.23.0
 
-toolchain go1.23.10
-
 require (
 	git.apache.org/thrift.git v0.0.0-20130818235439-ff980c143293
+	github.com/IBM/sarama v1.45.2
 	github.com/fsnotify/fsnotify v1.9.0
 	github.com/go-redis/redis/v8 v8.11.5
 	github.com/google/uuid v1.6.0
@@ -13,18 +12,21 @@ require (
 	github.com/natefinch/lumberjack v2.0.0+incompatible
 	github.com/pkg/errors v0.9.1
 	github.com/spf13/viper v1.10.1
+	github.com/stretchr/testify v1.10.0
 	go.uber.org/zap v1.21.0
+	google.golang.org/grpc v1.43.0
+	google.golang.org/protobuf v1.36.6
+	gopkg.in/yaml.v2 v2.4.0
 )
 
 require (
-	github.com/BurntSushi/toml v0.3.1 // indirect
-	github.com/IBM/sarama v1.45.2 // indirect
 	github.com/cespare/xxhash/v2 v2.1.2 // indirect
 	github.com/davecgh/go-spew v1.1.1 // indirect
 	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
 	github.com/eapache/go-resiliency v1.7.0 // indirect
 	github.com/eapache/go-xerial-snappy v0.0.0-20230731223053-c322873962e3 // indirect
 	github.com/eapache/queue v1.1.0 // indirect
+	github.com/golang/protobuf v1.5.2 // indirect
 	github.com/golang/snappy v0.0.4 // indirect
 	github.com/hashicorp/errwrap v1.0.0 // indirect
 	github.com/hashicorp/go-multierror v1.1.1 // indirect
@@ -36,18 +38,18 @@ require (
 	github.com/jcmturner/gokrb5/v8 v8.4.4 // indirect
 	github.com/jcmturner/rpc/v2 v2.0.3 // indirect
 	github.com/klauspost/compress v1.18.0 // indirect
-	github.com/kr/pretty v0.3.0 // indirect
+	github.com/kr/text v0.2.0 // indirect
 	github.com/magiconair/properties v1.8.5 // indirect
 	github.com/mitchellh/mapstructure v1.4.3 // indirect
 	github.com/pelletier/go-toml v1.9.4 // indirect
 	github.com/pierrec/lz4/v4 v4.1.22 // indirect
 	github.com/pmezard/go-difflib v1.0.0 // indirect
 	github.com/rcrowley/go-metrics v0.0.0-20201227073835-cf1acfcdf475 // indirect
+	github.com/rogpeppe/go-internal v1.6.1 // indirect
 	github.com/spf13/afero v1.6.0 // indirect
 	github.com/spf13/cast v1.4.1 // indirect
 	github.com/spf13/jwalterweatherman v1.1.0 // indirect
 	github.com/spf13/pflag v1.0.5 // indirect
-	github.com/stretchr/testify v1.10.0 // indirect
 	github.com/subosito/gotenv v1.2.0 // indirect
 	go.uber.org/atomic v1.7.0 // indirect
 	go.uber.org/multierr v1.6.0 // indirect
@@ -55,10 +57,8 @@ require (
 	golang.org/x/net v0.40.0 // indirect
 	golang.org/x/sys v0.33.0 // indirect
 	golang.org/x/text v0.25.0 // indirect
-	google.golang.org/protobuf v1.36.6 // indirect
-	gopkg.in/check.v1 v1.0.0-20201130134442-10cb98267c6c // indirect
+	google.golang.org/genproto v0.0.0-20211208223120-3a66f561d7aa // indirect
 	gopkg.in/ini.v1 v1.66.2 // indirect
 	gopkg.in/natefinch/lumberjack.v2 v2.0.0 // indirect
-	gopkg.in/yaml.v2 v2.4.0 // indirect
 	gopkg.in/yaml.v3 v3.0.1 // indirect
 )
diff --git a/go.sum b/go.sum
index 8326b65..3839980 100644
--- a/go.sum
+++ b/go.sum
@@ -1,13 +1,28 @@
+cloud.google.com/go v0.26.0/go.mod h1:aQUYkXzVsufM+DwF1aE+0xfcU+56JwCaLick0ClmMTw=
+cloud.google.com/go v0.34.0/go.mod h1:aQUYkXzVsufM+DwF1aE+0xfcU+56JwCaLick0ClmMTw=
 git.apache.org/thrift.git v0.0.0-20130818235439-ff980c143293 h1:ckgReIfiE5WSdI+dP623hKaeZQN4GCerrqHoNpQlO3E=
 git.apache.org/thrift.git v0.0.0-20130818235439-ff980c143293/go.mod h1:fPE2ZNJGynbRyZ4dJvy6G277gSllfV2HJqblrnkyeyg=
 github.com/BurntSushi/toml v0.3.1 h1:WXkYYl6Yr3qBf1K79EBnL4mak0OimBfB0XUf9Vl28OQ=
 github.com/BurntSushi/toml v0.3.1/go.mod h1:xHWCNGjB5oqiDr8zfno3MHue2Ht5sIBksp03qcyfWMU=
 github.com/IBM/sarama v1.45.2 h1:8m8LcMCu3REcwpa7fCP6v2fuPuzVwXDAM2DOv3CBrKw=
 github.com/IBM/sarama v1.45.2/go.mod h1:ppaoTcVdGv186/z6MEKsMm70A5fwJfRTpstI37kVn3Y=
+github.com/OneOfOne/xxhash v1.2.2/go.mod h1:HSdplMjZKSmBqAxg5vPj2TmRDmfkzw+cTzAElWljhcU=
+github.com/antihax/optional v1.0.0/go.mod h1:uupD/76wgC+ih3iEmQUL+0Ugr19nfwCT1kdvxnR2qWY=
 github.com/benbjohnson/clock v1.1.0 h1:Q92kusRqC1XV2MjkWETPvjJVqKetz1OzxZB7mHJLju8=
 github.com/benbjohnson/clock v1.1.0/go.mod h1:J11/hYXuz8f4ySSvYwY0FKfm+ezbsZBKZxNJlLklBHA=
+github.com/census-instrumentation/opencensus-proto v0.2.1/go.mod h1:f6KPmirojxKA12rnyqOA5BBL4O983OfeGPqjHWSTneU=
+github.com/cespare/xxhash v1.1.0/go.mod h1:XrSqR1VqqWfGrhpAt58auRo0WTKS1nRRg3ghfAqPWnc=
+github.com/cespare/xxhash/v2 v2.1.1/go.mod h1:VGX0DQ3Q6kWi7AoAeZDth3/j3BFtOZR5XLFGgcrjCOs=
 github.com/cespare/xxhash/v2 v2.1.2 h1:YRXhKfTDauu4ajMg1TPgFO5jnlC2HCbmLXMcTG5cbYE=
 github.com/cespare/xxhash/v2 v2.1.2/go.mod h1:VGX0DQ3Q6kWi7AoAeZDth3/j3BFtOZR5XLFGgcrjCOs=
+github.com/client9/misspell v0.3.4/go.mod h1:qj6jICC3Q7zFZvVWo7KLAzC3yx5G7kyvSDkc90ppPyw=
+github.com/cncf/udpa/go v0.0.0-20191209042840-269d4d468f6f/go.mod h1:M8M6+tZqaGXZJjfX53e64911xZQV5JYwmTeXPW+k8Sc=
+github.com/cncf/udpa/go v0.0.0-20201120205902-5459f2c99403/go.mod h1:WmhPx2Nbnhtbo57+VJT5O0JRkEi1Wbu0z5j0R8u5Hbk=
+github.com/cncf/udpa/go v0.0.0-20210930031921-04548b0d99d4/go.mod h1:6pvJx4me5XPnfI9Z40ddWsdw2W/uZgQLFXToKeRcDiI=
+github.com/cncf/xds/go v0.0.0-20210312221358-fbca930ec8ed/go.mod h1:eXthEFrGJvWHgFFCl3hGmgk+/aYT6PnTQLykKQRLhEs=
+github.com/cncf/xds/go v0.0.0-20210805033703-aa0b78936158/go.mod h1:eXthEFrGJvWHgFFCl3hGmgk+/aYT6PnTQLykKQRLhEs=
+github.com/cncf/xds/go v0.0.0-20210922020428-25de7278fc84/go.mod h1:eXthEFrGJvWHgFFCl3hGmgk+/aYT6PnTQLykKQRLhEs=
+github.com/cncf/xds/go v0.0.0-20211011173535-cb28da3451f1/go.mod h1:eXthEFrGJvWHgFFCl3hGmgk+/aYT6PnTQLykKQRLhEs=
 github.com/creack/pty v1.1.9/go.mod h1:oKZEueFk5CKHvIhNR5MUki03XCEU+Q6VDXinZuGJ33E=
 github.com/davecgh/go-spew v1.1.0/go.mod h1:J7Y8YcW2NihsgmVo/mv3lAwl/skON4iLHjSsI+c5H38=
 github.com/davecgh/go-spew v1.1.1 h1:vj9j/u1bqnvCEfJOwUhtlOARqs3+rkHYY13jYWTU97c=
@@ -20,16 +35,52 @@ github.com/eapache/go-xerial-snappy v0.0.0-20230731223053-c322873962e3 h1:Oy0F4A
 github.com/eapache/go-xerial-snappy v0.0.0-20230731223053-c322873962e3/go.mod h1:YvSRo5mw33fLEx1+DlK6L2VV43tJt5Eyel9n9XBcR+0=
 github.com/eapache/queue v1.1.0 h1:YOEu7KNc61ntiQlcEeUIoDTJ2o8mQznoNvUhiigpIqc=
 github.com/eapache/queue v1.1.0/go.mod h1:6eCeP0CKFpHLu8blIFXhExK/dRa7WDZfr6jVFPTqq+I=
+github.com/envoyproxy/go-control-plane v0.9.0/go.mod h1:YTl/9mNaCwkRvm6d1a2C3ymFceY/DCBVvsKhRF0iEA4=
+github.com/envoyproxy/go-control-plane v0.9.1-0.20191026205805-5f8ba28d4473/go.mod h1:YTl/9mNaCwkRvm6d1a2C3ymFceY/DCBVvsKhRF0iEA4=
+github.com/envoyproxy/go-control-plane v0.9.4/go.mod h1:6rpuAdCZL397s3pYoYcLgu1mIlRU8Am5FuJP05cCM98=
+github.com/envoyproxy/go-control-plane v0.9.9-0.20201210154907-fd9021fe5dad/go.mod h1:cXg6YxExXjJnVBQHBLXeUAgxn2UodCpnH306RInaBQk=
+github.com/envoyproxy/go-control-plane v0.9.9-0.20210512163311-63b5d3c536b0/go.mod h1:hliV/p42l8fGbc6Y9bQ70uLwIvmJyVE5k4iMKlh8wCQ=
+github.com/envoyproxy/go-control-plane v0.9.10-0.20210907150352-cf90f659a021/go.mod h1:AFq3mo9L8Lqqiid3OhADV3RfLJnjiw63cSpi+fDTRC0=
+github.com/envoyproxy/protoc-gen-validate v0.1.0/go.mod h1:iSmxcyjqTsJpI2R4NaDN7+kN2VEUnK/pcBlmesArF7c=
+github.com/fortytw2/leaktest v1.3.0 h1:u8491cBMTQ8ft8aeV+adlcytMZylmA5nnwwkRZjI8vw=
+github.com/fortytw2/leaktest v1.3.0/go.mod h1:jDsjWgpAGjm2CA7WthBh/CdZYEPF31XHquHwclZch5g=
 github.com/fsnotify/fsnotify v1.9.0 h1:2Ml+OJNzbYCTzsxtv8vKSFD9PbJjmhYF14k/jKC7S9k=
 github.com/fsnotify/fsnotify v1.9.0/go.mod h1:8jBTzvmWwFyi3Pb8djgCCO5IBqzKJ/Jwo8TRcHyHii0=
+github.com/ghodss/yaml v1.0.0/go.mod h1:4dBDuWmgqj2HViK6kFavaiC9ZROes6MMH2rRYeMEF04=
 github.com/go-redis/redis/v8 v8.11.5 h1:AcZZR7igkdvfVmQTPnu9WE37LRrO/YrBH5zWyjDC0oI=
 github.com/go-redis/redis/v8 v8.11.5/go.mod h1:gREzHqY1hg6oD9ngVRbLStwAWKhA0FEgq8Jd4h5lpwo=
+github.com/golang/glog v0.0.0-20160126235308-23def4e6c14b/go.mod h1:SBH7ygxi8pfUlaOkMMuAQtPIUF8ecWP5IEl/CR7VP2Q=
+github.com/golang/mock v1.1.1/go.mod h1:oTYuIxOrZwtPieC+H1uAHpcLFnEyAGVDL/k47Jfbm0A=
+github.com/golang/protobuf v1.2.0/go.mod h1:6lQm79b+lXiMfvg/cZm0SGofjICqVBUtrP5yJMmIC1U=
+github.com/golang/protobuf v1.3.2/go.mod h1:6lQm79b+lXiMfvg/cZm0SGofjICqVBUtrP5yJMmIC1U=
+github.com/golang/protobuf v1.3.3/go.mod h1:vzj43D7+SQXF/4pzW/hwtAqwc6iTitCiVSaWz5lYuqw=
+github.com/golang/protobuf v1.4.0-rc.1/go.mod h1:ceaxUfeHdC40wWswd/P6IGgMaK3YpKi5j83Wpe3EHw8=
+github.com/golang/protobuf v1.4.0-rc.1.0.20200221234624-67d41d38c208/go.mod h1:xKAWHe0F5eneWXFV3EuXVDTCmh+JuBKY0li0aMyXATA=
+github.com/golang/protobuf v1.4.0-rc.2/go.mod h1:LlEzMj4AhA7rCAGe4KMBDvJI+AwstrUpVNzEA03Pprs=
+github.com/golang/protobuf v1.4.0-rc.4.0.20200313231945-b860323f09d0/go.mod h1:WU3c8KckQ9AFe+yFwt9sWVRKCVIyN9cPHBJSNnbL67w=
+github.com/golang/protobuf v1.4.0/go.mod h1:jodUvKwWbYaEsadDk5Fwe5c77LiNKVO9IDvqG2KuDX0=
+github.com/golang/protobuf v1.4.1/go.mod h1:U8fpvMrcmy5pZrNK1lt4xCsGvpyWQ/VVv6QDs8UjoX8=
+github.com/golang/protobuf v1.4.2/go.mod h1:oDoupMAO8OvCJWAcko0GGGIgR6R6ocIYbsSw735rRwI=
+github.com/golang/protobuf v1.4.3/go.mod h1:oDoupMAO8OvCJWAcko0GGGIgR6R6ocIYbsSw735rRwI=
+github.com/golang/protobuf v1.5.0/go.mod h1:FsONVRAS9T7sI+LIUmWTfcYkHO4aIWwzhcaSAoJOfIk=
+github.com/golang/protobuf v1.5.2 h1:ROPKBNFfQgOUMifHyP+KYbvpjbdoFNs+aK7DXlji0Tw=
+github.com/golang/protobuf v1.5.2/go.mod h1:XVQd3VNwM+JqD3oG2Ue2ip4fOMUkwXdXDdiuN0vRsmY=
 github.com/golang/snappy v0.0.4 h1:yAGX7huGHXlcLOEtBnF4w7FQwA26wojNCwOYAEhLjQM=
 github.com/golang/snappy v0.0.4/go.mod h1:/XxbfmMg8lxefKM7IXC3fBNl/7bRcc72aCRzEWrmP2Q=
+github.com/google/go-cmp v0.2.0/go.mod h1:oXzfMopK8JAjlY9xF4vHSVASa0yLyX7SntLO5aqRK0M=
+github.com/google/go-cmp v0.3.0/go.mod h1:8QqcDgzrUqlUb/G2PQTWiueGozuR1884gddMywk6iLU=
+github.com/google/go-cmp v0.3.1/go.mod h1:8QqcDgzrUqlUb/G2PQTWiueGozuR1884gddMywk6iLU=
+github.com/google/go-cmp v0.4.0/go.mod h1:v8dTdLbMG2kIc/vJvl+f65V22dbkXbowE6jgT/gNBxE=
+github.com/google/go-cmp v0.5.0/go.mod h1:v8dTdLbMG2kIc/vJvl+f65V22dbkXbowE6jgT/gNBxE=
+github.com/google/go-cmp v0.5.5/go.mod h1:v8dTdLbMG2kIc/vJvl+f65V22dbkXbowE6jgT/gNBxE=
+github.com/google/go-cmp v0.5.6 h1:BKbKCqvP6I+rmFHt06ZmyQtvB8xAkWdhFyr0ZUNZcxQ=
+github.com/google/go-cmp v0.5.6/go.mod h1:v8dTdLbMG2kIc/vJvl+f65V22dbkXbowE6jgT/gNBxE=
+github.com/google/uuid v1.1.2/go.mod h1:TIyPZe4MgqvfeYDBFedMoGGpEw/LqOeaOT+nhxU+yHo=
 github.com/google/uuid v1.6.0 h1:NIvaJDMOsjHA8n1jAhLSgzrAzy1Hgr+hNrb57e+94F0=
 github.com/google/uuid v1.6.0/go.mod h1:TIyPZe4MgqvfeYDBFedMoGGpEw/LqOeaOT+nhxU+yHo=
 github.com/gorilla/securecookie v1.1.1/go.mod h1:ra0sb63/xPlUeL+yeDciTfxMRAA+MP+HVt/4epWDjd4=
 github.com/gorilla/sessions v1.2.1/go.mod h1:dk2InVEVJ0sfLlnXv9EAgkf6ecYs/i80K/zI+bUmuGM=
+github.com/grpc-ecosystem/grpc-gateway v1.16.0/go.mod h1:BDjrQk3hbvj6Nolgz8mAMFbcEtjT1g+wF4CSlocrBnw=
 github.com/hashicorp/errwrap v1.0.0 h1:hLrqtEDnRye3+sgx6z4qVLNuviH3MR5aQ0ykNJa/UYA=
 github.com/hashicorp/errwrap v1.0.0/go.mod h1:YH+1FKiLXxHSkmPseP+kNlulaMuP3n2brvKWEqk/Jc4=
 github.com/hashicorp/go-multierror v1.1.1 h1:H5DkEtf6CXdFp0N0Em5UCwQpXMWke8IA0+lD48awMYo=
@@ -45,6 +96,7 @@ github.com/jcmturner/dnsutils/v2 v2.0.0 h1:lltnkeZGL0wILNvrNiVCR6Ro5PGU/SeBvVO/8
 github.com/jcmturner/dnsutils/v2 v2.0.0/go.mod h1:b0TnjGOvI/n42bZa+hmXL+kFJZsFT7G4t3HTlQ184QM=
 github.com/jcmturner/gofork v1.7.6 h1:QH0l3hzAU1tfT3rZCnW5zXl+orbkNMMRGJfdJjHVETg=
 github.com/jcmturner/gofork v1.7.6/go.mod h1:1622LH6i/EZqLloHfE7IeZ0uEJwMSUyQ/nDd82IeqRo=
+github.com/jcmturner/goidentity/v6 v6.0.1 h1:VKnZd2oEIMorCTsFBnJWbExfNN7yZr3EhJAxwOkZg6o=
 github.com/jcmturner/goidentity/v6 v6.0.1/go.mod h1:X1YW3bgtvwAXju7V3LCIMpY0Gbxyjn/mY9zx4tFonSg=
 github.com/jcmturner/gokrb5/v8 v8.4.4 h1:x1Sv4HaTpepFkXbt2IkL29DXRf8sOfZXo8eRKh687T8=
 github.com/jcmturner/gokrb5/v8 v8.4.4/go.mod h1:1btQEpgT6k+unzCwX1KdWMEwPPkkgBtP+F6aCACiMrs=
@@ -54,7 +106,6 @@ github.com/klauspost/compress v1.18.0 h1:c/Cqfb0r+Yi+JtIEq73FWXVkRonBlf0CRNYc8Zt
 github.com/klauspost/compress v1.18.0/go.mod h1:2Pp+KzxcywXVXMr50+X0Q/Lsb43OQHYWRCY2AiWywWQ=
 github.com/kr/fs v0.1.0/go.mod h1:FFnZGqtBN9Gxj7eW1uZ42v5BccTP0vu6NEaFoC2HwRg=
 github.com/kr/pretty v0.1.0/go.mod h1:dAy3ld7l9f0ibDNOQOHHMYYIIbhfbHSm3C4ZsoJORNo=
-github.com/kr/pretty v0.2.1/go.mod h1:ipq/a2n7PKx3OHsz4KJII5eveXtPO4qwEXGdVfWzfnI=
 github.com/kr/pretty v0.3.0 h1:WgNl7dwNpEZ6jJ9k1snq4pZsg7DOEN8hP9Xw0Tsjwk0=
 github.com/kr/pretty v0.3.0/go.mod h1:640gp4NfQd8pI5XOwp5fnNeVWj67G7CFk/SaSQn7NBk=
 github.com/kr/pty v1.1.1/go.mod h1:pFQYn66WHrOpPYNljwOMqo10TkYh1fy3cYio2l3bCsQ=
@@ -85,10 +136,13 @@ github.com/pkg/errors v0.9.1/go.mod h1:bwawxfHBFNV+L2hUp1rHADufV3IMtnDRdf1r5NINE
 github.com/pkg/sftp v1.10.1/go.mod h1:lYOWFsE0bwd1+KfKJaKeuokY15vzFx25BLbzYYoAxZI=
 github.com/pmezard/go-difflib v1.0.0 h1:4DBwDE0NGyQoBHbLQYPwSUPoCMWR5BEzIk/f1lZbAQM=
 github.com/pmezard/go-difflib v1.0.0/go.mod h1:iKH77koFhYxTK1pcRnkKkqfTogsbg7gZNVY4sRDYZ/4=
+github.com/prometheus/client_model v0.0.0-20190812154241-14fe0d1b01d4/go.mod h1:xMI15A0UPsDsEKsMN9yxemIoYk6Tm2C1GtYGdfGttqA=
 github.com/rcrowley/go-metrics v0.0.0-20201227073835-cf1acfcdf475 h1:N/ElC8H3+5XpJzTSTfLsJV/mx9Q9g7kxmchpfZyxgzM=
 github.com/rcrowley/go-metrics v0.0.0-20201227073835-cf1acfcdf475/go.mod h1:bCqnVzQkZxMG4s8nGwiZ5l3QUCyqpo9Y+/ZMZ9VjZe4=
+github.com/rogpeppe/fastuuid v1.2.0/go.mod h1:jVj6XXZzXRy/MSR5jhDC/2q6DgLz+nrA6LYCDYWNEvQ=
 github.com/rogpeppe/go-internal v1.6.1 h1:/FiVV8dS/e+YqF2JvO3yXRFbBLTIuSDkuC7aBOAvL+k=
 github.com/rogpeppe/go-internal v1.6.1/go.mod h1:xXDCJY+GAPziupqXw64V24skbSoqbTEfhy4qGm1nDQc=
+github.com/spaolacci/murmur3 v0.0.0-20180118202830-f09979ecbc72/go.mod h1:JwIasOWyU6f++ZhiEuf87xNszmSA2myDM2Kzu9HwQUA=
 github.com/spf13/afero v1.6.0 h1:xoax2sJ2DT8S8xA2paPFjDCScCNeWsg75VG0DLRreiY=
 github.com/spf13/afero v1.6.0/go.mod h1:Ai8FlHk4v/PARR026UzYexafAt9roJ7LcLMAmO6Z93I=
 github.com/spf13/cast v1.4.1 h1:s0hze+J0196ZfEMTs80N7UlFt0BDuQ7Q+JDnHiMWKdA=
@@ -105,7 +159,7 @@ github.com/stretchr/objx v0.5.0/go.mod h1:Yh+to48EsGEfYuaHDzXPcE3xhTkx73EhmCGUpE
 github.com/stretchr/testify v1.2.2/go.mod h1:a8OnRcib4nhh0OaRAV+Yts87kKdq0PP7pXfy6kDkUVs=
 github.com/stretchr/testify v1.3.0/go.mod h1:M5WIy9Dh21IEIfnGCwXGc5bZfKNJtfHm1UVUgZn+9EI=
 github.com/stretchr/testify v1.4.0/go.mod h1:j7eGeouHqKxXV5pUuKE4zz7dFj8WfuZ+81PSLYec5m4=
-github.com/stretchr/testify v1.7.0 h1:nwc3DEeHmmLAfoZucVR881uASk0Mfjw8xYJ99tb5CcY=
+github.com/stretchr/testify v1.5.1/go.mod h1:5W2xD1RspED5o8YsWQXVCued0rvSQ+mT+I5cxcmMvtA=
 github.com/stretchr/testify v1.7.0/go.mod h1:6Fq8oRcR53rry900zMqJjRRixrwX3KX962/h/Wwjteg=
 github.com/stretchr/testify v1.7.1/go.mod h1:6Fq8oRcR53rry900zMqJjRRixrwX3KX962/h/Wwjteg=
 github.com/stretchr/testify v1.8.0/go.mod h1:yNjHg4UonilssWZ8iaSj1OCr/vHnekPRkoO+kdMU+MU=
@@ -116,6 +170,7 @@ github.com/subosito/gotenv v1.2.0 h1:Slr1R9HxAlEKefgq5jn9U+DnETlIUa6HfgEzj0g5d7s
 github.com/subosito/gotenv v1.2.0/go.mod h1:N0PQaV/YGNqwC0u51sEeR/aUtSLEXKX9iv69rRypqCw=
 github.com/yuin/goldmark v1.3.5/go.mod h1:mwnBkeHKe2W/ZEtQ+71ViKU8L12m81fl3OWwC1Zlc8k=
 github.com/yuin/goldmark v1.4.13/go.mod h1:6yULJ656Px+*****************************/CY=
+go.opentelemetry.io/proto/otlp v0.7.0/go.mod h1:PqfVotwruBrMGOCsRd/89rSnXhoiJIqeYNgFYFoEGnI=
 go.uber.org/atomic v1.7.0 h1:ADUqmZGgLDDfbSL9ZmPxKTybcoEYHgpYfELNoN+7hsw=
 go.uber.org/atomic v1.7.0/go.mod h1:fEN4uk6kAWBTFdckzkM89CLk9XfWZrxpCo0nPH17wJc=
 go.uber.org/goleak v1.1.11 h1:wy28qYRKZgnJTxGxvye5/wgWr1EKjmUDGYox5mGlRlI=
@@ -127,31 +182,48 @@ go.uber.org/zap v1.21.0/go.mod h1:wjWOCqI0f2ZZrJF/UufIOkiC8ii6tm1iqIsLo76RfJw=
 golang.org/x/crypto v0.0.0-20190308221718-c2843e01d9a2/go.mod h1:djNgcEr1/C05ACkg1iLfiJU5Ep61QUkGW8qpdssI0+w=
 golang.org/x/crypto v0.0.0-20190820162420-60c769a6c586/go.mod h1:yigFU9vqHzYiE8UmvKecakEJjdnWj3jj499lnFckfCI=
 golang.org/x/crypto v0.0.0-20191011191535-87dc89f01550/go.mod h1:yigFU9vqHzYiE8UmvKecakEJjdnWj3jj499lnFckfCI=
+golang.org/x/crypto v0.0.0-20200622213623-75b288015ac9/go.mod h1:LzIPMQfyMNhhGPhUkYOs5KpL4U8rLKemX1yGLhDgUto=
 golang.org/x/crypto v0.0.0-20210921155107-089bfa567519/go.mod h1:GvvjBRRGRdwPK5ydBHafDWAxML/pGHZbMvKqRZ5+Abc=
 golang.org/x/crypto v0.6.0/go.mod h1:OFC/31mSvZgRz0V1QTNCzfAI1aIRzbiufJtkMIlEp58=
 golang.org/x/crypto v0.38.0 h1:jt+WWG8IZlBnVbomuhg2Mdq0+BBQaHbtqHEFEigjUV8=
 golang.org/x/crypto v0.38.0/go.mod h1:MvrbAqul58NNYPKnOra203SB9vpuZW0e+RRZV+Ggqjw=
+golang.org/x/exp v0.0.0-20190121172915-509febef88a4/go.mod h1:CJ0aWSM057203Lf6IL+f9T1iT9GByDxfZKAQTCR3kQA=
+golang.org/x/lint v0.0.0-20181026193005-c67002cb31c3/go.mod h1:UVdnD1Gm6xHRNCYTkRU2/jEulfH38KcIWyp/GAMgvoE=
+golang.org/x/lint v0.0.0-20190227174305-5b3e6a55c961/go.mod h1:wehouNa3lNwaWXcvxsM5YxQ5yQlVC4a0KAMCusXpPoU=
+golang.org/x/lint v0.0.0-20190313153728-d0100b6bd8b3/go.mod h1:6SW0HCj/g11FgYtHlgUYUwCkIfeOF89ocIRzGO/8vkc=
 golang.org/x/lint v0.0.0-20190930215403-16217165b5de/go.mod h1:6SW0HCj/g11FgYtHlgUYUwCkIfeOF89ocIRzGO/8vkc=
 golang.org/x/mod v0.4.2/go.mod h1:s0Qsj1ACt9ePp/hMypM3fl4fZqREWJwdYDEqhRiZZUA=
 golang.org/x/mod v0.6.0-dev.0.20220419223038-86c51ed26bb4/go.mod h1:jJ57K6gSWd91VN4djpZkiMVwK6gcyfeH4XE8wZrZaV4=
+golang.org/x/net v0.0.0-20180724234803-3673e40ba225/go.mod h1:mL1N/T3taQHkDXs73rZJwtUhF3w3ftmwwsq0BUmARs4=
+golang.org/x/net v0.0.0-20180826012351-8a410e7b638d/go.mod h1:mL1N/T3taQHkDXs73rZJwtUhF3w3ftmwwsq0BUmARs4=
+golang.org/x/net v0.0.0-20190108225652-1e06a53dbb7e/go.mod h1:mL1N/T3taQHkDXs73rZJwtUhF3w3ftmwwsq0BUmARs4=
+golang.org/x/net v0.0.0-20190213061140-3a22650c66bd/go.mod h1:mL1N/T3taQHkDXs73rZJwtUhF3w3ftmwwsq0BUmARs4=
 golang.org/x/net v0.0.0-20190311183353-d8887717615a/go.mod h1:t9HGtf8HONx5eT2rtn7q6eTqICYqUVnKs3thJo3Qplg=
 golang.org/x/net v0.0.0-20190404232315-eb5bcb51f2a3/go.mod h1:t9HGtf8HONx5eT2rtn7q6eTqICYqUVnKs3thJo3Qplg=
 golang.org/x/net v0.0.0-20190620200207-3b0461eec859/go.mod h1:z5CRVTTTmAJ677TzLLGU+0bjPO0LkuOLi4/5GtJWs/s=
 golang.org/x/net v0.0.0-20200114155413-6afb5195e5aa/go.mod h1:z5CRVTTTmAJ677TzLLGU+0bjPO0LkuOLi4/5GtJWs/s=
+golang.org/x/net v0.0.0-20200822124328-c89045814202/go.mod h1:/O7V0waA8r7cgGh81Ro3o1hOxt32SMVPicZroKQ2sZA=
 golang.org/x/net v0.0.0-20210226172049-e18ecbb05110/go.mod h1:m0MpNAwzfU5UDzcl9v0D8zg8gWTRqZa9RBIspLL5mdg=
 golang.org/x/net v0.0.0-20210405180319-a5a99cb37ef4/go.mod h1:p54w0d4576C0XHj96bSt6lcn1PtDYWL6XObtHCRCNQM=
-golang.org/x/net v0.0.0-20210813160813-60bc85c4be6d h1:LO7XpTYMwTqxjLcGWPijK3vRXg1aWdlNOVOHRq45d7c=
-golang.org/x/net v0.0.0-20210813160813-60bc85c4be6d/go.mod h1:9nx3DQGgdP8bBQD5qxJ1jj9UTztislL4KSBs9R2vV5Y=
 golang.org/x/net v0.0.0-20220722155237-a158d28d115b/go.mod h1:XRhObCWvk6IyKnWLug+ECip1KBveYUHfp+8e9klMJ9c=
 golang.org/x/net v0.6.0/go.mod h1:2Tu9+aMcznHK/AK1HMvgo6xiTLG5rD5rZLDS+rp2Bjs=
 golang.org/x/net v0.7.0/go.mod h1:2Tu9+aMcznHK/AK1HMvgo6xiTLG5rD5rZLDS+rp2Bjs=
 golang.org/x/net v0.40.0 h1:79Xs7wF06Gbdcg4kdCCIQArK11Z1hr5POQ6+fIYHNuY=
 golang.org/x/net v0.40.0/go.mod h1:y0hY0exeL2Pku80/zKK7tpntoX23cqL3Oa6njdgRtds=
+golang.org/x/oauth2 v0.0.0-20180821212333-d2e6202438be/go.mod h1:N/0e6XlmueqKjAGxoOufVs8QHGRruUQn6yWY3a++T0U=
+golang.org/x/oauth2 v0.0.0-20200107190931-bf48bf16ab8d/go.mod h1:gOpvHmFTYa4IltrdGE7lF6nIHvwfUNPOp7c8zoXwtLw=
+golang.org/x/sync v0.0.0-20180314180146-1d60e4601c6f/go.mod h1:RxMgew5VJxzue5/jJTE5uejpjVlOe/izrB70Jof72aM=
+golang.org/x/sync v0.0.0-20181108010431-42b317875d0f/go.mod h1:RxMgew5VJxzue5/jJTE5uejpjVlOe/izrB70Jof72aM=
+golang.org/x/sync v0.0.0-20181221193216-37e7f081c4d4/go.mod h1:RxMgew5VJxzue5/jJTE5uejpjVlOe/izrB70Jof72aM=
 golang.org/x/sync v0.0.0-20190423024810-112230192c58/go.mod h1:RxMgew5VJxzue5/jJTE5uejpjVlOe/izrB70Jof72aM=
 golang.org/x/sync v0.0.0-20210220032951-036812b2e83c/go.mod h1:RxMgew5VJxzue5/jJTE5uejpjVlOe/izrB70Jof72aM=
 golang.org/x/sync v0.0.0-20220722155255-886fb9371eb4/go.mod h1:RxMgew5VJxzue5/jJTE5uejpjVlOe/izrB70Jof72aM=
+golang.org/x/sync v0.14.0 h1:woo0S4Yywslg6hp4eUFjTVOyKt0RookbpAHG4c1HmhQ=
+golang.org/x/sync v0.14.0/go.mod h1:1dzgHSNfp02xaA81J2MS99Qcpr2w7fw1gpm99rleRqA=
+golang.org/x/sys v0.0.0-20180830151530-49385e6e1522/go.mod h1:STP8DvDyc/dI5b8T5hshtkjS+E42TnysNCUPdjciGhY=
 golang.org/x/sys v0.0.0-20190215142949-d0b11bdaac8a/go.mod h1:STP8DvDyc/dI5b8T5hshtkjS+E42TnysNCUPdjciGhY=
 golang.org/x/sys v0.0.0-20190412213103-97732733099d/go.mod h1:h1NjWce9XRLGQEsW7wpKNCjG9DtNlClVuFLEZdDNbEs=
+golang.org/x/sys v0.0.0-20200323222414-85ca7c5b95cd/go.mod h1:h1NjWce9XRLGQEsW7wpKNCjG9DtNlClVuFLEZdDNbEs=
 golang.org/x/sys v0.0.0-20201119102817-f84b799fce68/go.mod h1:h1NjWce9XRLGQEsW7wpKNCjG9DtNlClVuFLEZdDNbEs=
 golang.org/x/sys v0.0.0-20210330210617-4fbd30eecc44/go.mod h1:h1NjWce9XRLGQEsW7wpKNCjG9DtNlClVuFLEZdDNbEs=
 golang.org/x/sys v0.0.0-20210510120138-977fb7262007/go.mod h1:oPkhp1MJrh7nUepCBck5+mAzfO9JrbApNNgaTdGDITg=
@@ -159,8 +231,6 @@ golang.org/x/sys v0.0.0-20210615035016-665e8c7367d1/go.mod h1:oPkhp1MJrh7nUepCBc
 golang.org/x/sys v0.0.0-20220520151302-bc2c85ada10a/go.mod h1:oPkhp1MJrh7nUepCBck5+mAzfO9JrbApNNgaTdGDITg=
 golang.org/x/sys v0.0.0-20220722155257-8c9f86f7a55f/go.mod h1:oPkhp1MJrh7nUepCBck5+mAzfO9JrbApNNgaTdGDITg=
 golang.org/x/sys v0.5.0/go.mod h1:oPkhp1MJrh7nUepCBck5+mAzfO9JrbApNNgaTdGDITg=
-golang.org/x/sys v0.13.0 h1:Af8nKPmuFypiUBjVoU9V20FiaFXOcuZI21p0ycVYYGE=
-golang.org/x/sys v0.13.0/go.mod h1:oPkhp1MJrh7nUepCBck5+mAzfO9JrbApNNgaTdGDITg=
 golang.org/x/sys v0.33.0 h1:q3i8TbbEz+JRD9ywIRlyRAQbM0qF7hu24q3teo2hbuw=
 golang.org/x/sys v0.33.0/go.mod h1:BJP2sWEmIv4KK5OTEluFJCKSidICx8ciO85XgH3Ak8k=
 golang.org/x/term v0.0.0-20201126162022-7de9c90e9dd1/go.mod h1:bj7SfCRtBDWHUb9snDiAeCFNEtKQo2Wmx5Cou7ajbmo=
@@ -168,19 +238,53 @@ golang.org/x/term v0.0.0-20210927222741-03fcf44c2211/go.mod h1:jbD1KX2456YbFQfuX
 golang.org/x/term v0.5.0/go.mod h1:jMB1sMXY+tzblOD4FWmEbocvup2/aLOaQEp7JmGp78k=
 golang.org/x/text v0.3.0/go.mod h1:NqM8EUOU14njkJ3fqMW+pc6Ldnwhi/IjpwHt7yyuwOQ=
 golang.org/x/text v0.3.3/go.mod h1:5Zoc/QRtKVWzQhOtBMvqHzDpF6irO9z98xDceosuGiQ=
-golang.org/x/text v0.3.7 h1:olpwvP2KacW1ZWvsR7uQhoyTYvKAupfQrRGBFM352Gk=
+golang.org/x/text v0.3.5/go.mod h1:5Zoc/QRtKVWzQhOtBMvqHzDpF6irO9z98xDceosuGiQ=
 golang.org/x/text v0.3.7/go.mod h1:u+2+/6zg+i71rQMx5EYifcz6MCKuco9NR6JIITiCfzQ=
 golang.org/x/text v0.7.0/go.mod h1:mrYo+phRRbMaCq/xk9113O4dZlRixOauAjOtrjsXDZ8=
 golang.org/x/text v0.25.0 h1:qVyWApTSYLk/drJRO5mDlNYskwQznZmkpV2c8q9zls4=
 golang.org/x/text v0.25.0/go.mod h1:WEdwpYrmk1qmdHvhkSTNPm3app7v4rsT8F2UD6+VHIA=
 golang.org/x/tools v0.0.0-20180917221912-90fa682c2a6e/go.mod h1:n7NCudcB/nEzxVGmLbDWY5pfWTLqBcC2KZ6jyYvM4mQ=
+golang.org/x/tools v0.0.0-20190114222345-bf090417da8b/go.mod h1:n7NCudcB/nEzxVGmLbDWY5pfWTLqBcC2KZ6jyYvM4mQ=
+golang.org/x/tools v0.0.0-20190226205152-f727befe758c/go.mod h1:9Yl7xja0Znq3iFh3HoIrodX9oNMXvdceNzlUR8zjMvY=
 golang.org/x/tools v0.0.0-20190311212946-11955173bddd/go.mod h1:LCzVGOaR6xXOjkQ3onu1FJEFr0SW1gC7cKk1uF8kGRs=
+golang.org/x/tools v0.0.0-20190524140312-2c0ae7006135/go.mod h1:RgjU9mgBXZiqYHBnxXauZ1Gv1EHHAz9KjViQ78xBX0Q=
 golang.org/x/tools v0.0.0-20191119224855-298f0cb1881e/go.mod h1:b+2E5dAYhXwXZwtnZ6UAqBI28+e2cm9otk0dWdXHAEo=
 golang.org/x/tools v0.1.5/go.mod h1:o0xws9oXOQQZyjljx8fwUC0k7L1pTE6eaCbjGeHmOkk=
 golang.org/x/tools v0.1.12/go.mod h1:hNGJHUnrk76NpqgfD5Aqm5Crs+Hm0VOH/i9J2+nxYbc=
 golang.org/x/xerrors v0.0.0-20190717185122-a985d3407aa7/go.mod h1:I/5z698sn9Ka8TeJc9MKroUUfqBBauWjQqLJ2OPfmY0=
 golang.org/x/xerrors v0.0.0-20191011141410-1b5146add898/go.mod h1:I/5z698sn9Ka8TeJc9MKroUUfqBBauWjQqLJ2OPfmY0=
+golang.org/x/xerrors v0.0.0-20191204190536-9bdfabe68543/go.mod h1:I/5z698sn9Ka8TeJc9MKroUUfqBBauWjQqLJ2OPfmY0=
+golang.org/x/xerrors v0.0.0-20200804184101-5ec99f83aff1 h1:go1bK/D/BFZV2I8cIQd1NKEZ+0owSTG1fDTci4IqFcE=
 golang.org/x/xerrors v0.0.0-20200804184101-5ec99f83aff1/go.mod h1:I/5z698sn9Ka8TeJc9MKroUUfqBBauWjQqLJ2OPfmY0=
+google.golang.org/appengine v1.1.0/go.mod h1:EbEs0AVv82hx2wNQdGPgUI5lhzA/G0D9YwlJXL52JkM=
+google.golang.org/appengine v1.4.0/go.mod h1:xpcJRLb0r/rnEns0DIKYYv+WjYCduHsrkT7/EB5XEv4=
+google.golang.org/genproto v0.0.0-20180817151627-c66870c02cf8/go.mod h1:JiN7NxoALGmiZfu7CAH4rXhgtRTLTxftemlI0sWmxmc=
+google.golang.org/genproto v0.0.0-20190819201941-24fa4b261c55/go.mod h1:DMBHOl98Agz4BDEuKkezgsaosCRResVns1a3J2ZsMNc=
+google.golang.org/genproto v0.0.0-20200513103714-09dca8ec2884/go.mod h1:55QSHmfGQM9UVYDPBsyGGes0y52j32PQ3BqQfXhyH3c=
+google.golang.org/genproto v0.0.0-20200526211855-cb27e3aa2013/go.mod h1:NbSheEEYHJ7i3ixzK3sjbqSGDJWnxyFXZblF3eUsNvo=
+google.golang.org/genproto v0.0.0-20211208223120-3a66f561d7aa h1:I0YcKz0I7OAhddo7ya8kMnvprhcWM045PmkBdMO9zN0=
+google.golang.org/genproto v0.0.0-20211208223120-3a66f561d7aa/go.mod h1:5CzLGKJ67TSI2B9POpiiyGha0AjJvZIUgRMt1dSmuhc=
+google.golang.org/grpc v1.19.0/go.mod h1:mqu4LbDTu4XGKhr4mRzUsmM4RtVoemTSY81AxZiDr8c=
+google.golang.org/grpc v1.23.0/go.mod h1:Y5yQAOtifL1yxbo5wqy6BxZv8vAUGQwXBOALyacEbxg=
+google.golang.org/grpc v1.25.1/go.mod h1:c3i+UQWmh7LiEpx4sFZnkU36qjEYZ0imhYfXVyQciAY=
+google.golang.org/grpc v1.27.0/go.mod h1:qbnxyOmOxrQa7FizSgH+ReBfzJrCY1pSN7KXBS8abTk=
+google.golang.org/grpc v1.33.1/go.mod h1:fr5YgcSWrqhRRxogOsw7RzIpsmvOZ6IcH4kBYTpR3n0=
+google.golang.org/grpc v1.36.0/go.mod h1:qjiiYl8FncCW8feJPdyg3v6XW24KsRHe+dy9BAGRRjU=
+google.golang.org/grpc v1.40.0/go.mod h1:ogyxbiOoUXAkP+4+xa6PZSE9DZgIHtSpzjDTB9KAK34=
+google.golang.org/grpc v1.43.0 h1:Eeu7bZtDZ2DpRCsLhUlcrLnvYaMK1Gz86a+hMVvELmM=
+google.golang.org/grpc v1.43.0/go.mod h1:k+4IHHFw41K8+bbowsex27ge2rCb65oeWqe4jJ590SU=
+google.golang.org/protobuf v0.0.0-20200109180630-ec00e32a8dfd/go.mod h1:DFci5gLYBciE7Vtevhsrf46CRTquxDuWsQurQQe4oz8=
+google.golang.org/protobuf v0.0.0-20200221191635-4d8936d0db64/go.mod h1:kwYJMbMJ01Woi6D6+Kah6886xMZcty6N08ah7+eCXa0=
+google.golang.org/protobuf v0.0.0-20200228230310-ab0ca4ff8a60/go.mod h1:cfTl7dwQJ+fmap5saPgwCLgHXTUD7jkjRqWcaiX5VyM=
+google.golang.org/protobuf v1.20.1-0.20200309200217-e05f789c0967/go.mod h1:A+miEFZTKqfCUM6K7xSMQL9OKL/b6hQv+e19PK+JZNE=
+google.golang.org/protobuf v1.21.0/go.mod h1:47Nbq4nVaFHyn7ilMalzfO3qCViNmqZ2kzikPIcrTAo=
+google.golang.org/protobuf v1.22.0/go.mod h1:EGpADcykh3NcUnDUJcl1+ZksZNG86OlYog2l/sGQquU=
+google.golang.org/protobuf v1.23.0/go.mod h1:EGpADcykh3NcUnDUJcl1+ZksZNG86OlYog2l/sGQquU=
+google.golang.org/protobuf v1.23.1-0.20200526195155-81db48ad09cc/go.mod h1:EGpADcykh3NcUnDUJcl1+ZksZNG86OlYog2l/sGQquU=
+google.golang.org/protobuf v1.25.0/go.mod h1:9JNX74DMeImyA3h4bdi1ymwjUzf21/xIlbajtzgsN7c=
+google.golang.org/protobuf v1.26.0-rc.1/go.mod h1:jlhhOSvTdKEhbULTjvd4ARK9grFBp09yW+WbY/TyQbw=
+google.golang.org/protobuf v1.26.0/go.mod h1:9q0QmTI4eRPtz6boOQmLYwt+qCgq0jsYwAQnmE0givc=
+google.golang.org/protobuf v1.27.1/go.mod h1:9q0QmTI4eRPtz6boOQmLYwt+qCgq0jsYwAQnmE0givc=
 google.golang.org/protobuf v1.36.6 h1:z1NpPI8ku2WgiWnf+t9wTPsn6eP1L7ksHUlkfLvd9xY=
 google.golang.org/protobuf v1.36.6/go.mod h1:jduwjTPXsFjZGTmRluh+L6NjiWu7pchiJ2/5YcXBHnY=
 gopkg.in/check.v1 v0.0.0-20161208181325-20d25e280405/go.mod h1:Co6ibVJAznAaIkqp8huTwlJQCZ016jof/cbN4VW5Yz0=
@@ -195,6 +299,7 @@ gopkg.in/natefinch/lumberjack.v2 v2.0.0/go.mod h1:l0ndWWf7gzL7RNwBG7wST/UCcT4T24
 gopkg.in/tomb.v1 v1.0.0-20141024135613-dd632973f1e7 h1:uRGJdciOHaEIrze2W8Q3AKkepLTh2hOroT7a+7czfdQ=
 gopkg.in/tomb.v1 v1.0.0-20141024135613-dd632973f1e7/go.mod h1:dt/ZhP58zS4L8KSrWDmTeBkI65Dw0HsyUHuEVlX15mw=
 gopkg.in/yaml.v2 v2.2.2/go.mod h1:hI93XBmqTisBFMUTm0b8Fm+jr3Dg1NNxqwp+5A1VGuI=
+gopkg.in/yaml.v2 v2.2.3/go.mod h1:hI93XBmqTisBFMUTm0b8Fm+jr3Dg1NNxqwp+5A1VGuI=
 gopkg.in/yaml.v2 v2.2.8/go.mod h1:hI93XBmqTisBFMUTm0b8Fm+jr3Dg1NNxqwp+5A1VGuI=
 gopkg.in/yaml.v2 v2.4.0 h1:D8xgwECY7CYvx+Y2n4sBz93Jn9JRvxdiyyo8CTfuKaY=
 gopkg.in/yaml.v2 v2.4.0/go.mod h1:RDklbk79AGWmwhnvt/jBztapEOGDOx6ZbXqjP6csGnQ=
@@ -202,3 +307,5 @@ gopkg.in/yaml.v3 v3.0.0-20200313102051-9f266ea9e77c/go.mod h1:K4uyk7z7BCEPqu6E+C
 gopkg.in/yaml.v3 v3.0.0-20210107192922-496545a6307b/go.mod h1:K4uyk7z7BCEPqu6E+C64Yfv1cQ7kz7rIZviUmN+EgEM=
 gopkg.in/yaml.v3 v3.0.1 h1:fxVm/GzAzEWqLHuvctI91KS9hhNmmWOoWu0XTYJS7CA=
 gopkg.in/yaml.v3 v3.0.1/go.mod h1:K4uyk7z7BCEPqu6E+C64Yfv1cQ7kz7rIZviUmN+EgEM=
+honnef.co/go/tools v0.0.0-20190102054323-c2f93a96b099/go.mod h1:rf3lG4BRIbNafJWhAfAdb/ePZxsR/4RtNHQocxwk9r4=
+honnef.co/go/tools v0.0.0-20190523083050-ea95bdfd59fc/go.mod h1:rf3lG4BRIbNafJWhAfAdb/ePZxsR/4RtNHQocxwk9r4=
diff --git a/internal/model_index/predict/bid_decision.go b/internal/model_index/predict/bid_decision.go
index 6fa1b55..399f922 100644
--- a/internal/model_index/predict/bid_decision.go
+++ b/internal/model_index/predict/bid_decision.go
@@ -224,8 +224,9 @@ func (b *BidDecision) calculateOcpmBid(strategy *rtb_adinfo_types.RTBStrategy, p
 func (b *BidDecision) calculateOcpcBid(strategy *rtb_adinfo_types.RTBStrategy, predictResult *PredictResult, basePrice int64) (int64, int64) {
 	switch strategy.MediaBidType {
 	case 1: //CPM，需要根据媒体CPC策略进行转换
+		// PreCtr 是百万分之一的单位，需要除以 1000000
 		if predictResult.PreCtr > 0 {
-			return int64(float64(basePrice) * float64(predictResult.PreCtr)), 0
+			return int64(float64(basePrice) * float64(predictResult.PreCtr) / 1000000.0), 0
 		}
 		return 0, strategy.InternalAcostLimit
 	case 2: //媒体CPC直接返回价格
diff --git a/internal/model_index/predict/predict_processor.go b/internal/model_index/predict/predict_processor.go
index 2b241c9..5d813f8 100644
--- a/internal/model_index/predict/predict_processor.go
+++ b/internal/model_index/predict/predict_processor.go
@@ -1,7 +1,8 @@
 package predict
 
 import (
-	"rtb_model_server/common/domob_thrift/predict_model_server"
+	"context"
+	"fmt"
 	"rtb_model_server/conf"
 	ctx "rtb_model_server/internal/context"
 	"rtb_model_server/internal/zaplog"
@@ -12,50 +13,95 @@ import (
 )
 
 type PredictProcessor struct {
-	connPool    *ConnectionPool
-	monitorStop chan struct{}
-	monitorWg   sync.WaitGroup
+	tfClient         *TFServingClient
+	featureProcessor *FeatureProcessor
+	monitorStop      chan struct{}
+	monitorWg        sync.WaitGroup
 }
 
 func NewPredictProcessor() *PredictProcessor {
 	config := conf.GlobalConfig.PredictServer
-	connTimeout := time.Duration(config.ConnTimeout) * time.Millisecond
 
-	connPool := NewConnectionPool(
-		config.Addr,
-		config.PoolSize,
-		config.MaxIdleConns,
-		connTimeout,
-	)
+	// 创建 TensorFlow Serving 客户端
+	tfClient, err := NewTFServingClient(config.TFServingAddr, config.ModelName)
+	if err != nil {
+		zaplog.Logger.Error("Failed to create TensorFlow Serving client",
+			zap.String("addr", config.TFServingAddr),
+			zap.String("model", config.ModelName),
+			zap.Error(err))
+		// 如果创建失败，返回 nil 或者 panic，根据项目需求决定
+		panic(fmt.Sprintf("Failed to create TensorFlow Serving client: %v", err))
+	}
+
+	// 创建特征处理器
+	featureProcessor, err := NewFeatureProcessor(config.FeatureConfigPath)
+	if err != nil {
+		zaplog.Logger.Error("Failed to create feature processor",
+			zap.String("config_path", config.FeatureConfigPath),
+			zap.Error(err))
+		// 如果创建失败，返回 nil 或者 panic，根据项目需求决定
+		panic(fmt.Sprintf("Failed to create feature processor: %v", err))
+	}
 
 	p := &PredictProcessor{
-		connPool:    connPool,
-		monitorStop: make(chan struct{}),
+		tfClient:         tfClient,
+		featureProcessor: featureProcessor,
+		monitorStop:      make(chan struct{}),
 	}
 
-	// 启动连接池监控协程
-	p.startPoolMonitor()
+	// 启动监控协程
+	p.startMonitor()
+
+	zaplog.Logger.Info("PredictProcessor initialized successfully",
+		zap.String("tf_serving_addr", config.TFServingAddr),
+		zap.String("model_name", config.ModelName),
+		zap.String("feature_config", config.FeatureConfigPath))
 
 	return p
 }
 
-// Close 关闭连接池
+// Close 关闭处理器
 func (p *PredictProcessor) Close() {
 	// 停止监控协程
 	close(p.monitorStop)
 	p.monitorWg.Wait()
 
-	// 关闭连接池
-	p.connPool.Close()
+	// 关闭 TensorFlow Serving 客户端
+	if p.tfClient != nil {
+		p.tfClient.Close()
+	}
+
+	zaplog.Logger.Info("PredictProcessor closed")
 }
 
-// GetPoolStats 获取连接池统计信息
-func (p *PredictProcessor) GetPoolStats() (total, inUse, idle int) {
-	return p.connPool.GetPoolStats()
+// GetStats 获取处理器统计信息
+func (p *PredictProcessor) GetStats() map[string]interface{} {
+	stats := make(map[string]interface{})
+
+	// 测试 TensorFlow Serving 连接状态
+	if p.tfClient != nil {
+		if err := p.tfClient.TestConnection(); err != nil {
+			stats["tf_serving_status"] = "disconnected"
+			stats["tf_serving_error"] = err.Error()
+		} else {
+			stats["tf_serving_status"] = "connected"
+		}
+	} else {
+		stats["tf_serving_status"] = "not_initialized"
+	}
+
+	// 特征处理器统计
+	if p.featureProcessor != nil {
+		stats["feature_count"] = p.featureProcessor.GetFeatureCount()
+		stats["hash_feature_count"] = p.featureProcessor.GetHashFeatureCount()
+		stats["raw_feature_count"] = p.featureProcessor.GetRawFeatureCount()
+	}
+
+	return stats
 }
 
-// startPoolMonitor 启动连接池监控协程
-func (p *PredictProcessor) startPoolMonitor() {
+// startMonitor 启动监控协程
+func (p *PredictProcessor) startMonitor() {
 	p.monitorWg.Add(1)
 	go func() {
 		defer p.monitorWg.Done()
@@ -65,27 +111,23 @@ func (p *PredictProcessor) startPoolMonitor() {
 		ticker := time.NewTicker(monitorInterval)
 		defer ticker.Stop()
 
-		zaplog.Logger.Info("Connection pool monitor started")
+		zaplog.Logger.Info("TensorFlow Serving monitor started")
 
 		for {
 			select {
 			case <-p.monitorStop:
-				zaplog.Logger.Info("Connection pool monitor stopped")
+				zaplog.Logger.Info("TensorFlow Serving monitor stopped")
 				return
 			case <-ticker.C:
-				// 获取连接池统计信息
-				total, inUse, idle := p.GetPoolStats()
-				zaplog.Logger.Info("Connection Pool Stats", zap.Int("total", total), zap.Int("inUse", inUse), zap.Int("idle", idle))
-
-				// 可以在这里添加更多监控逻辑，比如:
-				// - 检查连接池使用率是否过高
-				// - 记录监控指标到监控系统
-				// - 在连接池耗尽时发出告警
-				if total > 0 {
-					usageRate := float64(inUse) / float64(total) * 100
-					if usageRate > 80 {
-						zaplog.Logger.Warn("WARNING: Connection pool usage rate is high", zap.Float64("usageRate", usageRate))
-					}
+				// 获取处理器统计信息
+				stats := p.GetStats()
+				zaplog.Logger.Info("TensorFlow Serving Stats",
+					zap.Any("stats", stats))
+
+				// 检查连接状态
+				if status, ok := stats["tf_serving_status"].(string); ok && status != "connected" {
+					zaplog.Logger.Warn("WARNING: TensorFlow Serving connection issue",
+						zap.String("status", status))
 				}
 			}
 		}
@@ -101,85 +143,278 @@ type PredictResult struct {
 
 // 调用预估服务，返回预估结果
 func (p *PredictProcessor) Predict(requestCtx *ctx.RequestContext, creativeIds []int32) (map[int32]PredictResult, error) {
-	// 从连接池获取连接
-	conn, err := p.connPool.GetConnection()
-	if err != nil {
-		return nil, err
+	zaplog.Logger.Debug("Starting batch prediction",
+		zap.String("req_id", requestCtx.BidRequest.ReqId),
+		zap.Int("creative_count", len(creativeIds)))
+
+	if len(creativeIds) == 0 {
+		return make(map[int32]PredictResult), nil
 	}
-	defer p.connPool.ReleaseConnection(conn)
 
-	req := p.buildRequestHeader(requestCtx, creativeIds)
-	resp, err := conn.client.GetPredictValue(req)
+	// 批量提取特征
+	batchFeatures := make([]map[string]interface{}, 0, len(creativeIds))
+	validCreativeIds := make([]int32, 0, len(creativeIds))
+
+	for _, creativeId := range creativeIds {
+		// 提取特征
+		features, err := p.extractFeatures(requestCtx, creativeId)
+		if err != nil {
+			zaplog.Logger.Error("Failed to extract features",
+				zap.Int32("creative_id", creativeId),
+				zap.Error(err))
+			continue
+		}
+
+		batchFeatures = append(batchFeatures, features)
+		validCreativeIds = append(validCreativeIds, creativeId)
+	}
+
+	if len(batchFeatures) == 0 {
+		zaplog.Logger.Warn("No valid features extracted",
+			zap.String("req_id", requestCtx.BidRequest.ReqId))
+		return make(map[int32]PredictResult), nil
+	}
+
+	// 批量调用 TensorFlow Serving 进行预测
+	ctx, cancel := context.WithTimeout(context.Background(),
+		time.Duration(conf.GlobalConfig.PredictServer.ConnTimeout)*time.Millisecond)
+	defer cancel()
+
+	batchPrediction, err := p.tfClient.Predict(ctx, batchFeatures)
 	if err != nil {
-		return nil, err
+		zaplog.Logger.Error("TensorFlow Serving batch prediction failed",
+			zap.String("req_id", requestCtx.BidRequest.ReqId),
+			zap.Int("batch_size", len(batchFeatures)),
+			zap.Error(err))
+		return nil, fmt.Errorf("batch prediction failed: %w", err)
 	}
-	results := p.buildResponse(resp)
-	return results, nil
-}
-func (p *PredictProcessor) buildResponse(predictRsp *predict_model_server.PredictModelServerResponse) map[int32]PredictResult {
-	results := make(map[int32]PredictResult)
-	for _, ad := range predictRsp.ResponseList {
 
-		results[int32(ad.Cid)] = PredictResult{
-			CreativeId: int32(ad.Cid),
-			PreCtr:     ad.Ctr,
-			PreCvr:     ad.Cvr,
-			PreDeepCvr: ad.DeepCvr,
+	// 解析批量预测结果
+	results := make(map[int32]PredictResult)
+	for i, creativeId := range validCreativeIds {
+		result, err := p.parseBatchPredictionResult(creativeId, batchPrediction, i)
+		if err != nil {
+			zaplog.Logger.Error("Failed to parse batch prediction result",
+				zap.Int32("creative_id", creativeId),
+				zap.Int("batch_index", i),
+				zap.Error(err))
+			continue
 		}
+
+		results[creativeId] = result
 	}
-	return results
+
+	zaplog.Logger.Debug("Batch prediction completed",
+		zap.String("req_id", requestCtx.BidRequest.ReqId),
+		zap.Int("success_count", len(results)),
+		zap.Int("total_count", len(creativeIds)),
+		zap.Int("batch_size", len(batchFeatures)))
+
+	return results, nil
 }
 
-func (p *PredictProcessor) buildRequestHeader(requestCtx *ctx.RequestContext, creativeIds []int32) *predict_model_server.PredictModelServerRequest {
+// extractFeatures 从 RTB 请求中提取特征
+func (p *PredictProcessor) extractFeatures(requestCtx *ctx.RequestContext, creativeId int32) (map[string]interface{}, error) {
 	bidReq := requestCtx.BidRequest
 	now := time.Now()
-	req := &predict_model_server.PredictModelServerRequest{
-		ReqId:         bidReq.ReqId,
-		ReqTs:         int32(now.Unix()),
-		ExchangeId:    bidReq.ExchangeId,
-		SearchId:      bidReq.SearchId,
-		User:          bidReq.User,
-		Device:        bidReq.Device,
-		App:           bidReq.App,
-		AdxExchangeId: bidReq.AdxExchangeId,
-		ModelName:     conf.GlobalConfig.PredictServer.ModelName,
-	}
-	req.AdList = p.buildAdList(requestCtx, creativeIds)
-	return req
+
+	// 获取创意信息
+	creative, err := requestCtx.AdIndex.GetCreative(creativeId)
+	if err != nil {
+		return nil, fmt.Errorf("get creative failed: %w", err)
+	}
+
+	// 获取策略信息
+	strategy, err := requestCtx.AdIndex.GetStrategy(creative.StrategyId)
+	if err != nil {
+		return nil, fmt.Errorf("get strategy failed: %w", err)
+	}
+
+	// 构建原始特征 - 固定46个特征（45个预定义 + dsp_bid）
+	// 严格按照 TensorFlow Serving 官网标准和您 demo 中的特征顺序
+	rawFeatures := map[string]interface{}{
+		// 45个预定义特征（按字母顺序，与模型 signature 完全匹配）
+		"absolute_pos":      int64(0),                                     // 绝对位置
+		"ad_source":         int64(0),                                     // 广告来源
+		"adp_dim":           int64(0),                                     // 广告位尺寸
+		"adp_id":            int64(0),                                     // 广告位ID
+		"api_version":       int64(0),                                     // API版本
+		"app_bundle":        p.convertStringToInt64(bidReq.App.AppBundle), // 应用包名
+		"app_name":          p.convertStringToInt64(bidReq.App.AppBundle), // 应用名称
+		"app_package_name":  p.convertStringToInt64(bidReq.App.AppBundle), // 应用包名
+		"bid_id":            int64(0),                                     // 出价ID
+		"bidfloor":          int64(0),                                     // 底价
+		"budget_type_v1":    int64(0),                                     // 预算类型
+		"campaign_id":       int64(creative.CampaignId),                   // 活动ID
+		"cat_id":            int64(0),                                     // 分类ID
+		"city":              int64(bidReq.Device.GeoCity),                 // 城市
+		"client_ip":         p.convertIPToInt64(bidReq.Device.Ip),         // 客户端IP
+		"client_ipv6":       int64(0),                                     // 客户端IPv6
+		"country":           int64(bidReq.Device.GeoCountry),              // 国家
+		"creative_id":       int64(creativeId),                            // 创意ID
+		"creative_type":     int64(creative.DisplayType),                  // 创意类型
+		"dev_make":          int64(0),                                     // 设备制造商
+		"dev_model":         int64(0),                                     // 设备型号
+		"dm_media_id":       int64(bidReq.App.DmMediaId),                  // 媒体ID
+		"dm_platform":       int64(bidReq.Device.DmPlatform),              // 平台
+		"domob_bidfloor":    int64(0),                                     // Domob底价
+		"dsp_ad_slot":       int64(0),                                     // DSP广告位
+		"dsp_advertiser_id": int64(0),                                     // DSP广告主ID
+		"dsp_cost_mod":      int64(0),                                     // DSP成本模式
+		"dsp_creative_id":   int64(0),                                     // DSP创意ID
+		"dsp_id":            int64(1),                                     // DSP ID（固定值）
+		"exchange_id":       int64(bidReq.ExchangeId),                     // 交易所ID
+		"exp_id":            int64(0),                                     // 实验ID
+		"hour":              int64(now.Hour()),                            // 小时
+		"inventory_type":    int64(0),                                     // 库存类型
+		"media_bid_type":    int64(strategy.MediaBidType),                 // 媒体出价类型
+		"province":          int64(bidReq.Device.GeoRegion),               // 省份
+		"schain":            int64(0),                                     // 供应链
+		"sponsor_id":        int64(creative.SponsorId),                    // 赞助商ID
+		"standard_make":     int64(0),                                     // 标准制造商
+		"strategy_id":       int64(creative.StrategyId),                   // 策略ID
+		"surge_score":       int64(0),                                     // 激增分数
+		"tanx_ad_id":        int64(0),                                     // Tanx广告ID
+		"tanx_group_id":     int64(0),                                     // Tanx组ID
+		"tanx_task_id":      int64(0),                                     // Tanx任务ID
+		"template_id":       int64(creative.TemplateId),                   // 模板ID
+		"week_day":          int64(now.Weekday()),                         // 星期几
+
+		// 第46个特征：dsp_bid（单独配置）
+		"dsp_bid": int64(strategy.Price), // DSP出价
+	}
+
+	// 使用特征处理器处理特征
+	processedFeatures, err := p.featureProcessor.ProcessFeatures(rawFeatures)
+	if err != nil {
+		return nil, fmt.Errorf("process features failed: %w", err)
+	}
+
+	zaplog.Logger.Debug("Features extracted",
+		zap.Int32("creative_id", creativeId),
+		zap.Int("raw_feature_count", len(rawFeatures)),
+		zap.Int("processed_feature_count", len(processedFeatures)))
+
+	return processedFeatures, nil
 }
 
-// 这里注意下，如果需要预估的创意太多，会导致预估超时，
-func (p *PredictProcessor) buildAdList(requestCtx *ctx.RequestContext, creativeIds []int32) []*predict_model_server.PredictAdInfo {
-	var index int32
-	result := make([]*predict_model_server.PredictAdInfo, 0)
-	for _, cid := range creativeIds {
-		creative, err := requestCtx.AdIndex.GetCreative(cid)
-		if err != nil {
-			continue
+// parsePredictionResult 解析 TensorFlow Serving 预测结果
+func (p *PredictProcessor) parsePredictionResult(creativeId int32, prediction map[string]interface{}) (PredictResult, error) {
+	result := PredictResult{
+		CreativeId: creativeId,
+		PreCtr:     0,
+		PreCvr:     0,
+		PreDeepCvr: 0,
+	}
+
+	// 解析预测结果
+	// TensorFlow Serving 返回的结果格式通常是 {"outputs": {"output_name": [[value1, value2, ...]]}}
+	if outputs, ok := prediction["outputs"].(map[string]interface{}); ok {
+		// 假设模型输出包含 ctr 和 cvr 预测
+		if ctrOutput, exists := outputs["ctr"]; exists {
+			if ctrValues, ok := ctrOutput.([][]float32); ok && len(ctrValues) > 0 && len(ctrValues[0]) > 0 {
+				// 将 float32 转换为百万分之一的 int64
+				result.PreCtr = int64(ctrValues[0][0] * 1000000)
+			}
 		}
-		tracking, err := requestCtx.AdIndex.GetAdTracking(creative.AdTrackingIds[0])
-		if err != nil {
-			continue
+
+		if cvrOutput, exists := outputs["cvr"]; exists {
+			if cvrValues, ok := cvrOutput.([][]float32); ok && len(cvrValues) > 0 && len(cvrValues[0]) > 0 {
+				// 将 float32 转换为百万分之一的 int64
+				result.PreCvr = int64(cvrValues[0][0] * 1000000)
+			}
 		}
-		strategy, err := requestCtx.AdIndex.GetStrategy(creative.StrategyId)
-		if err != nil {
-			continue
+
+		if deepCvrOutput, exists := outputs["deep_cvr"]; exists {
+			if deepCvrValues, ok := deepCvrOutput.([][]float32); ok && len(deepCvrValues) > 0 && len(deepCvrValues[0]) > 0 {
+				// 将 float32 转换为百万分之一的 int64
+				result.PreDeepCvr = int64(deepCvrValues[0][0] * 1000000)
+			}
 		}
-		ad := &predict_model_server.PredictAdInfo{
-			AdIndex:    index,
-			CreativeId: cid,
-			SponsorId:  creative.SponsorId,
-			CampaignId: creative.CampaignId,
-			StrategyId: creative.StrategyId,
-			TemplateId: creative.TemplateId,
-			CostType:   strategy.MediaCostType,
-			Bid:        strategy.Price,
-			BidType:    strategy.MediaBidType,
-			ProductId:  tracking.ProductId,
+	}
+
+	zaplog.Logger.Debug("Parsed prediction result",
+		zap.Int32("creative_id", creativeId),
+		zap.Int64("pre_ctr", result.PreCtr),
+		zap.Int64("pre_cvr", result.PreCvr),
+		zap.Int64("pre_deep_cvr", result.PreDeepCvr))
+
+	return result, nil
+}
+
+// parseBatchPredictionResult 解析批量 TensorFlow Serving 预测结果中的单个样本
+func (p *PredictProcessor) parseBatchPredictionResult(creativeId int32, batchPrediction map[string]interface{}, batchIndex int) (PredictResult, error) {
+	result := PredictResult{
+		CreativeId: creativeId,
+		PreCtr:     0,
+		PreCvr:     0,
+		PreDeepCvr: 0,
+	}
+
+	// 解析批量预测结果
+	// TensorFlow Serving 返回的批量结果格式通常是 {"output_name": [batch_values]}
+	for outputName, outputValues := range batchPrediction {
+		switch outputName {
+		case "ctr", "pre_ctr":
+			if values, ok := outputValues.([]float32); ok && batchIndex < len(values) {
+				// 将 float32 转换为百万分之一的 int64
+				result.PreCtr = int64(values[batchIndex] * 1000000)
+			} else if values, ok := outputValues.([]int64); ok && batchIndex < len(values) {
+				result.PreCtr = values[batchIndex]
+			}
+
+		case "cvr", "pre_cvr":
+			if values, ok := outputValues.([]float32); ok && batchIndex < len(values) {
+				// 将 float32 转换为百万分之一的 int64
+				result.PreCvr = int64(values[batchIndex] * 1000000)
+			} else if values, ok := outputValues.([]int64); ok && batchIndex < len(values) {
+				result.PreCvr = values[batchIndex]
+			}
+
+		case "deep_cvr", "pre_deep_cvr":
+			if values, ok := outputValues.([]float32); ok && batchIndex < len(values) {
+				// 将 float32 转换为百万分之一的 int64
+				result.PreDeepCvr = int64(values[batchIndex] * 1000000)
+			} else if values, ok := outputValues.([]int64); ok && batchIndex < len(values) {
+				result.PreDeepCvr = values[batchIndex]
+			}
 		}
-		result = append(result, ad)
-		index++
+	}
+
+	zaplog.Logger.Debug("Parsed batch prediction result",
+		zap.Int32("creative_id", creativeId),
+		zap.Int("batch_index", batchIndex),
+		zap.Int64("pre_ctr", result.PreCtr),
+		zap.Int64("pre_cvr", result.PreCvr),
+		zap.Int64("pre_deep_cvr", result.PreDeepCvr))
+
+	return result, nil
+}
 
+// convertIPToInt64 将IP地址转换为int64
+func (p *PredictProcessor) convertIPToInt64(ip string) int64 {
+	// 简单的IP转换，实际可能需要更复杂的处理
+	// 这里可以使用hash或者其他转换方式
+	if ip == "" {
+		return 0
+	}
+	// 使用简单的hash转换
+	hash := int64(0)
+	for _, b := range []byte(ip) {
+		hash = hash*31 + int64(b)
+	}
+	return hash
+}
+
+// convertStringToInt64 将字符串转换为int64
+func (p *PredictProcessor) convertStringToInt64(s string) int64 {
+	if s == "" {
+		return 0
+	}
+	// 使用简单的hash转换
+	hash := int64(0)
+	for _, b := range []byte(s) {
+		hash = hash*31 + int64(b)
 	}
-	return result
+	return hash
 }
